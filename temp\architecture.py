
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Optional, Tuple, List, Dict
from dataclasses import dataclass

@dataclass
class DiffusionConfig:
	vocab_size: int = 50257
	hidden_dim: int = 768
	num_layers: int = 12
	num_heads: int = 12
	max_length: int = 1024
	diffusion_steps: int = 14
	st_memory_size: int = 100
	mt_memory_size: int = 500
	lt_memory_size: int = 2000
	dropout: float = 0.1
	layer_norm_eps: float = 1e-5

# ============================================================================
# PHASE 1: FOUNDATION COMPONENTS
# ============================================================================

class DiffusionScheduler(nn.Module):
	def __init__(self, total_steps: int = 14, initial_edit_ratio: float = 0.8):
		super().__init__()
		self.total_steps = total_steps
		self.initial_edit_ratio = initial_edit_ratio
		self.register_buffer('step_schedule', self._create_schedule())

	def _create_schedule(self) -> torch.Tensor:
		"""Create edit count schedule for each diffusion step"""
		steps = torch.arange(self.total_steps, dtype=torch.float32)
		schedule = torch.clamp(1.0 - steps / self.total_steps, min=0.1)
		return schedule * self.initial_edit_ratio

	def get_edit_count(self, step: int, sequence_length: int) -> int:
		"""Calculate number of tokens to edit at current step"""
		ratio = self.step_schedule[step].item()
		edit_count = max(1, int(sequence_length * ratio))
		return min(edit_count, sequence_length)

	def get_temperature(self, step: int, temp_start: float = 1.5, temp_end: float = 0.1) -> float:
		"""Calculate temperature for current step using exponential annealing"""
		progress = step / self.total_steps
		alpha = -math.log(temp_end / temp_start)
		temperature = temp_start * math.exp(-alpha * progress)
		return temperature

class AttentionSelector(nn.Module):
	def __init__(self, hidden_dim: int, num_heads: int = 8):
		super().__init__()
		self.hidden_dim = hidden_dim
		self.num_heads = num_heads
		self.head_dim = hidden_dim // num_heads

		self.importance_head = nn.Linear(hidden_dim, 1)
		self.query_proj = nn.Linear(hidden_dim, hidden_dim)
		self.key_proj = nn.Linear(hidden_dim, hidden_dim)
		self.value_proj = nn.Linear(hidden_dim, hidden_dim)
		self.out_proj = nn.Linear(hidden_dim, hidden_dim)

	def select_tokens_to_edit(self, hidden_states: torch.Tensor,
							confidence_scores: torch.Tensor,
							edit_count: int) -> torch.Tensor:
		"""Select tokens with lowest confidence for editing"""
		batch_size, seq_len = confidence_scores.shape

		# Compute importance scores
		importance = self.importance_head(hidden_states).squeeze(-1)  # [B, L]

		# Combine confidence and importance (lower is worse)
		selection_scores = confidence_scores + 0.1 * importance

		# Select top-k worst tokens
		_, indices = torch.topk(selection_scores, edit_count, dim=-1, largest=False)
		return indices

	def compute_attention_weights(self, query: torch.Tensor, key: torch.Tensor,
								value: torch.Tensor, mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
		"""Multi-head attention computation"""
		batch_size, seq_len = query.shape[:2]

		# Project and reshape
		Q = self.query_proj(query).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
		K = self.key_proj(key).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
		V = self.value_proj(value).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)

		# Scaled dot-product attention
		scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.head_dim)

		if mask is not None:
			scores = scores.masked_fill(mask == 0, float('-inf'))

		attention_weights = F.softmax(scores, dim=-1)
		attended_values = torch.matmul(attention_weights, V)

		# Concatenate heads and project
		attended_values = attended_values.transpose(1, 2).contiguous().view(
			batch_size, seq_len, self.hidden_dim)
		output = self.out_proj(attended_values)

		return output, attention_weights

class BaseTransformer(nn.Module):
	def __init__(self, config: DiffusionConfig):
		super().__init__()
		self.config = config

		# Token and position embeddings
		self.token_embedding = nn.Embedding(config.vocab_size, config.hidden_dim)
		self.position_embedding = nn.Embedding(config.max_length, config.hidden_dim)

		# Transformer layers
		self.layers = nn.ModuleList([
			TransformerLayer(config) for _ in range(config.num_layers)
		])

		# Memory cross-attention layer
		self.memory_attention = AttentionSelector(config.hidden_dim, config.num_heads)
		self.memory_norm = nn.LayerNorm(config.hidden_dim, eps=config.layer_norm_eps)

		# Output head
		self.ln_f = nn.LayerNorm(config.hidden_dim, eps=config.layer_norm_eps)
		self.lm_head = nn.Linear(config.hidden_dim, config.vocab_size, bias=False)

		self.dropout = nn.Dropout(config.dropout)

	def forward(self, input_ids: torch.Tensor,
				memory_states: Optional[torch.Tensor] = None,
				attention_mask: Optional[torch.Tensor] = None,
				position_ids: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:

		batch_size, seq_len = input_ids.shape
		device = input_ids.device

		# Generate position IDs if not provided
		if position_ids is None:
			position_ids = torch.arange(seq_len, device=device).unsqueeze(0).expand(batch_size, -1)

		# Embeddings
		token_emb = self.token_embedding(input_ids)
		pos_emb = self.position_embedding(position_ids)
		hidden_states = self.dropout(token_emb + pos_emb)

		# Store attention weights for analysis
		attention_weights = []

		# Pass through transformer layers
		for layer in self.layers:
			hidden_states, layer_attn = layer(hidden_states, attention_mask)
			attention_weights.append(layer_attn)

		# Cross-attention to memory if provided
		if memory_states is not None:
			memory_output, memory_attn = self.cross_attend_to_memory(hidden_states, memory_states)
			hidden_states = self.memory_norm(hidden_states + memory_output)
			attention_weights.append(memory_attn)

		# Final layer norm and output projection
		hidden_states = self.ln_f(hidden_states)
		logits = self.lm_head(hidden_states)

		return {
			'hidden_states': hidden_states,
			'logits': logits,
			'attention_weights': attention_weights
		}

	def cross_attend_to_memory(self, hidden_states: torch.Tensor,
							 memory_states: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
		"""Cross-attention between current sequence and memory"""
		memory_output, memory_attn = self.memory_attention.compute_attention_weights(
			query=hidden_states,
			key=memory_states,
			value=memory_states
		)
		return memory_output, memory_attn

class TransformerLayer(nn.Module):
	def __init__(self, config: DiffusionConfig):
		super().__init__()
		self.attention = AttentionSelector(config.hidden_dim, config.num_heads)
		self.feed_forward = nn.Sequential(
			nn.Linear(config.hidden_dim, 4 * config.hidden_dim),
			nn.GELU(),
			nn.Linear(4 * config.hidden_dim, config.hidden_dim),
			nn.Dropout(config.dropout)
		)
		self.ln1 = nn.LayerNorm(config.hidden_dim, eps=config.layer_norm_eps)
		self.ln2 = nn.LayerNorm(config.hidden_dim, eps=config.layer_norm_eps)
		self.dropout = nn.Dropout(config.dropout)

	def forward(self, hidden_states: torch.Tensor,
				attention_mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:

		# Self-attention with residual connection
		normed_states = self.ln1(hidden_states)
		attn_output, attn_weights = self.attention.compute_attention_weights(
			normed_states, normed_states, normed_states, attention_mask
		)
		hidden_states = hidden_states + self.dropout(attn_output)

		# Feed-forward with residual connection
		ff_output = self.feed_forward(self.ln2(hidden_states))
		hidden_states = hidden_states + ff_output

		return hidden_states, attn_weights

# ============================================================================
# PHASE 2: EXTERNAL MEMORY SYSTEM
# ============================================================================

class MemoryManager(nn.Module):
	def __init__(self, config: DiffusionConfig):
		super().__init__()
		self.config = config

		self.short_term = ShortTermMemory(config.st_memory_size, config.hidden_dim)
		self.medium_term = MediumTermMemory(config.mt_memory_size, config.hidden_dim)
		self.long_term = LongTermMemory(config.lt_memory_size, config.hidden_dim)

		# Memory consolidation networks
		self.st_to_mt_proj = nn.Linear(config.hidden_dim, config.hidden_dim)
		self.mt_to_lt_proj = nn.Linear(config.hidden_dim, config.hidden_dim)

	def update_memories(self, new_content: torch.Tensor,
					   new_hidden: torch.Tensor, step: int) -> None:
		"""Update all memory levels with new content"""
		# Always update short-term memory
		self.short_term.append(new_content, new_hidden)

		# Consolidate to medium-term every few steps
		if step % 3 == 0:
			st_summary = self.short_term.get_summary()
			if st_summary is not None:
				mt_content = self.st_to_mt_proj(st_summary)
				self.medium_term.consolidate_from_short_term(mt_content)

		# Consolidate to long-term less frequently
		if step % 7 == 0:
			mt_summary = self.medium_term.get_summary()
			if mt_summary is not None:
				lt_content = self.mt_to_lt_proj(mt_summary)
				self.long_term.store_knowledge_patterns(lt_content)

	def read_memory_context(self, query_hidden_states: torch.Tensor) -> torch.Tensor:
		"""Retrieve relevant information from all memory levels"""
		batch_size, seq_len, hidden_dim = query_hidden_states.shape

		# Get memory contexts
		st_context = self.short_term.get_context()
		mt_context = self.medium_term.retrieve_relevant(query_hidden_states.mean(dim=1))
		lt_context = self.long_term.query_knowledge(query_hidden_states.mean(dim=1))

		# Combine memory contexts
		memory_contexts = []
		if st_context is not None:
			memory_contexts.append(st_context)
		if mt_context is not None:
			memory_contexts.append(mt_context)
		if lt_context is not None:
			memory_contexts.append(lt_context)

		if memory_contexts:
			combined_memory = torch.cat(memory_contexts, dim=1)
			return combined_memory
		else:
			return torch.zeros(batch_size, 1, hidden_dim, device=query_hidden_states.device)

class ShortTermMemory(nn.Module):
	def __init__(self, capacity: int, hidden_dim: int):
		super().__init__()
		self.capacity = capacity
		self.hidden_dim = hidden_dim

		# Circular buffers
		self.register_buffer('token_buffer', torch.zeros(capacity, dtype=torch.long))
		self.register_buffer('hidden_buffer', torch.zeros(capacity, hidden_dim))
		self.register_buffer('position', torch.tensor(0))
		self.register_buffer('filled', torch.tensor(0))

	def append(self, tokens: torch.Tensor, hidden_states: torch.Tensor) -> None:
		"""Add new tokens and hidden states to memory"""
		batch_size, seq_len = tokens.shape

		for b in range(batch_size):
			for s in range(seq_len):
				pos = self.position.item()
				self.token_buffer[pos] = tokens[b, s]
				self.hidden_buffer[pos] = hidden_states[b, s]

				self.position = (self.position + 1) % self.capacity
				self.filled = min(self.filled + 1, self.capacity)

	def get_context(self, window_size: int = 50) -> Optional[torch.Tensor]:
		"""Return most recent window_size items"""
		if self.filled == 0:
			return None

		actual_window = min(window_size, self.filled.item())

		# Get indices for most recent items
		if self.filled < self.capacity:
			start_idx = max(0, self.position - actual_window)
			indices = torch.arange(start_idx, self.position)
		else:
			start_pos = (self.position - actual_window) % self.capacity
			if start_pos < self.position:
				indices = torch.arange(start_pos, self.position)
			else:
				indices = torch.cat([
					torch.arange(start_pos, self.capacity),
					torch.arange(0, self.position)
				])

		context = self.hidden_buffer[indices].unsqueeze(0)  # Add batch dimension
		return context

	def get_summary(self) -> Optional[torch.Tensor]:
		"""Get summarized representation of short-term memory"""
		if self.filled == 0:
			return None

		valid_states = self.hidden_buffer[:self.filled]
		return valid_states.mean(dim=0)

class MediumTermMemory(nn.Module):
	def __init__(self, capacity: int, hidden_dim: int):
		super().__init__()
		self.capacity = capacity
		self.hidden_dim = hidden_dim

		self.register_buffer('memory_buffer', torch.zeros(capacity, hidden_dim))
		self.register_buffer('importance_scores', torch.zeros(capacity))
		self.register_buffer('position', torch.tensor(0))
		self.register_buffer('filled', torch.tensor(0))

		# Importance scoring network
		self.importance_net = nn.Sequential(
			nn.Linear(hidden_dim, hidden_dim // 2),
			nn.ReLU(),
			nn.Linear(hidden_dim // 2, 1),
			nn.Sigmoid()
		)

	def consolidate_from_short_term(self, st_content: torch.Tensor) -> None:
		"""Add consolidated content from short-term memory"""
		importance = self.importance_net(st_content).squeeze()

		pos = self.position.item()
		self.memory_buffer[pos] = st_content
		self.importance_scores[pos] = importance

		self.position = (self.position + 1) % self.capacity
		self.filled = min(self.filled + 1, self.capacity)

	def retrieve_relevant(self, query_embedding: torch.Tensor, top_k: int = 10) -> Optional[torch.Tensor]:
		"""Retrieve most relevant memories based on query"""
		if self.filled == 0:
			return None

		valid_memories = self.memory_buffer[:self.filled]

		# Compute similarity scores
		query_norm = F.normalize(query_embedding.unsqueeze(0), dim=-1)
		memory_norm = F.normalize(valid_memories, dim=-1)
		similarities = torch.matmul(query_norm, memory_norm.T).squeeze(0)

		# Weight by importance
		importance_weights = self.importance_scores[:self.filled]
		weighted_scores = similarities * importance_weights

		# Get top-k most relevant
		k = min(top_k, self.filled.item())
		_, top_indices = torch.topk(weighted_scores, k)

		relevant_memories = valid_memories[top_indices].unsqueeze(0)  # Add batch dimension
		return relevant_memories

	def get_summary(self) -> Optional[torch.Tensor]:
		"""Get importance-weighted summary of medium-term memory"""
		if self.filled == 0:
			return None

		valid_memories = self.memory_buffer[:self.filled]
		valid_importance = self.importance_scores[:self.filled]

		# Weighted average
		weights = F.softmax(valid_importance, dim=0)
		summary = torch.sum(valid_memories * weights.unsqueeze(-1), dim=0)
		return summary

class LongTermMemory(nn.Module):
	def __init__(self, capacity: int, hidden_dim: int):
		super().__init__()
		self.capacity = capacity
		self.hidden_dim = hidden_dim

		self.register_buffer('knowledge_buffer', torch.zeros(capacity, hidden_dim))
		self.register_buffer('access_counts', torch.zeros(capacity))
		self.register_buffer('position', torch.tensor(0))
		self.register_buffer('filled', torch.tensor(0))

		# Knowledge encoding network
		self.knowledge_encoder = nn.Sequential(
			nn.Linear(hidden_dim, hidden_dim),
			nn.LayerNorm(hidden_dim),
			nn.ReLU(),
			nn.Linear(hidden_dim, hidden_dim)
		)

	def store_knowledge_patterns(self, consolidated_info: torch.Tensor) -> None:
		"""Store generalized knowledge patterns"""
		encoded_knowledge = self.knowledge_encoder(consolidated_info)

		pos = self.position.item()
		self.knowledge_buffer[pos] = encoded_knowledge
		self.access_counts[pos] = 0  # Reset access count for new knowledge

		self.position = (self.position + 1) % self.capacity
		self.filled = min(self.filled + 1, self.capacity)

	def query_knowledge(self, context_embedding: torch.Tensor, top_k: int = 5) -> Optional[torch.Tensor]:
		"""Retrieve relevant background knowledge"""
		if self.filled == 0:
			return None

		valid_knowledge = self.knowledge_buffer[:self.filled]

		# Compute relevance scores
		context_norm = F.normalize(context_embedding.unsqueeze(0), dim=-1)
		knowledge_norm = F.normalize(valid_knowledge, dim=-1)
		relevance_scores = torch.matmul(context_norm, knowledge_norm.T).squeeze(0)

		# Get top-k most relevant
		k = min(top_k, self.filled.item())
		_, top_indices = torch.topk(relevance_scores, k)

		# Update access counts
		self.access_counts[top_indices] += 1

		relevant_knowledge = valid_knowledge[top_indices].unsqueeze(0)  # Add batch dimension
		return relevant_knowledge

# ============================================================================
# PHASE 3: CONFIDENCE AND TOKEN OPERATIONS
# ============================================================================

class ConfidenceEstimator(nn.Module):
	def __init__(self, hidden_dim: int):
		super().__init__()
		self.hidden_dim = hidden_dim

		# Confidence prediction head
		self.confidence_head = nn.Sequential(
			nn.Linear(hidden_dim, hidden_dim // 2),
			nn.ReLU(),
			nn.Dropout(0.1),
			nn.Linear(hidden_dim // 2, 1),
			nn.Sigmoid()
		)

		# Uncertainty estimation via Monte Carlo Dropout
		self.mc_dropout = nn.Dropout(0.1)

	def estimate_token_confidence(self, hidden_states: torch.Tensor,
								logits: torch.Tensor,
								mc_samples: int = 5) -> torch.Tensor:
		"""Estimate per-token confidence scores"""
		batch_size, seq_len, hidden_dim = hidden_states.shape

		# Method 1: Direct confidence prediction
		direct_confidence = self.confidence_head(hidden_states).squeeze(-1)

		# Method 2: Entropy-based uncertainty
		probs = F.softmax(logits, dim=-1)
		entropy = -torch.sum(probs * torch.log(probs + 1e-8), dim=-1)
		normalized_entropy = 1.0 - (entropy / math.log(probs.size(-1)))

		# Method 3: Monte Carlo Dropout uncertainty
		if self.training:
			mc_confidences = []
			for _ in range(mc_samples):
				mc_hidden = self.mc_dropout(hidden_states)
				mc_conf = self.confidence_head(mc_hidden).squeeze(-1)
				mc_confidences.append(mc_conf)

			mc_mean = torch.stack(mc_confidences).mean(dim=0)
			mc_std = torch.stack(mc_confidences).std(dim=0)
			mc_confidence = mc_mean * (1.0 - mc_std)  # Lower confidence for high variance
		else:
			mc_confidence = direct_confidence

		# Combine different confidence estimates
		combined_confidence = (direct_confidence + normalized_entropy + mc_confidence) / 3.0
		return combined_confidence

	def compute_sequence_confidence(self, token_confidences: torch.Tensor,
								  attention_weights: Optional[torch.Tensor] = None) -> torch.Tensor:
		"""Aggregate token-level confidences to sequence-level"""
		if attention_weights is not None:
			# Weight by attention (assuming last layer, last head)
			weights = attention_weights[-1][:, -1, :, :].mean(dim=1)  # [B, L]
			weighted_confidence = (token_confidences * weights).sum(dim=-1) / weights.sum(dim=-1)
		else:
			# Simple average
			weighted_confidence = token_confidences.mean(dim=-1)

		return weighted_confidence

class TokenEditor(nn.Module):
	def __init__(self, vocab_size: int, hidden_dim: int):
		super().__init__()
		self.vocab_size = vocab_size
		self.hidden_dim = hidden_dim

		# Token generation head for replacements
		self.token_generator = nn.Linear(hidden_dim, vocab_size)

	def insert_tokens(self, sequence: torch.Tensor, positions: torch.Tensor,
					 new_tokens: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
		"""Insert tokens at specified positions"""
		batch_size, seq_len = sequence.shape

		# Create new sequence with insertions
		# This is a simplified version - in practice, you'd need more sophisticated handling
		new_sequence = sequence.clone()
		position_map = torch.arange(seq_len).unsqueeze(0).expand(batch_size, -1)

		# For each batch and position, insert the new token
		for b in range(batch_size):
			for i, pos in enumerate(positions[b]):
				if pos < seq_len - 1:
					# Shift tokens to the right
					new_sequence[b, pos+1:] = sequence[b, pos:-1]
					new_sequence[b, pos] = new_tokens[b, i]
					position_map[b, pos+1:] += 1

		return new_sequence, position_map

	def remove_tokens(self, sequence: torch.Tensor, positions: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
		"""Remove tokens at specified positions"""
		batch_size, seq_len = sequence.shape

		new_sequence = sequence.clone()
		position_map = torch.arange(seq_len).unsqueeze(0).expand(batch_size, -1)

		# Sort positions in descending order to avoid index shifting issues
		sorted_positions, _ = torch.sort(positions, dim=-1, descending=True)

		for b in range(batch_size):
			for pos in sorted_positions[b]:
				if pos < seq_len - 1:
					# Shift tokens to the left
					new_sequence[b, pos:-1] = new_sequence[b, pos+1:]
					new_sequence[b, -1] = 0  # Pad token
					position_map[b, pos:-1] -= 1

		return new_sequence, position_map

	def replace_tokens(self, sequence: torch.Tensor, positions: torch.Tensor,
					  new_tokens: torch.Tensor) -> torch.Tensor:
		"""Replace tokens at specified positions"""
		new_sequence = sequence.clone()

		batch_size = sequence.size(0)
		for b in range(batch_size):
			for i, pos in enumerate(positions[b]):
				if pos < sequence.size(1):
					new_sequence[b, pos] = new_tokens[b, i]

		return new_sequence

	def generate_replacement_tokens(self, hidden_states: torch.Tensor,
								  positions: torch.Tensor,
								  temperature: float = 1.0) -> torch.Tensor:
		"""Generate new tokens for replacement"""
		batch_size = hidden_states.size(0)
		num_positions = positions.size(1)

		# Get hidden states at specified positions
		pos_hidden = torch.zeros(batch_size, num_positions, self.hidden_dim,
							   device=hidden_states.device)

		for b in range(batch_size):
			for i, pos in enumerate(positions[b]):
				if pos < hidden_states.size(1):
					pos_hidden[b, i] = hidden_states[b, pos]

		# Generate logits and sample
		logits = self.token_generator(pos_hidden) / temperature
		new_tokens = torch.multinomial(F.softmax(logits.view(-1, self.vocab_size), dim=-1),
									 num_samples=1).view(batch_size, num_positions)

		return new_tokens

class PositionTracker(nn.Module):
	def __init__(self, max_length: int):
		super().__init__()
		self.max_length = max_length
		self.register_buffer('base_positions', torch.arange(max_length))

	def update_positions(self, edit_operations: List[Dict]) -> torch.Tensor:
		"""Update position mappings based on edit operations"""
		# This would track position changes through insertions/deletions
		# For now, return base positions
		return self.base_positions[:self.max_length].clone()

	def get_attention_mask(self, sequence_length: int,
						  causal: bool = True) -> torch.Tensor:
		"""Generate attention mask considering position changes"""
		mask = torch.ones(sequence_length, sequence_length)

		if causal:
			# Causal mask - can't attend to future positions
			mask = torch.tril(mask)

		return mask.unsqueeze(0)  # Add batch dimension

# ============================================================================
# PHASE 4: PROGRESSIVE REFINEMENT
# ============================================================================

class ProgressiveRefiner(nn.Module):
	def __init__(self, total_steps: int, transition_point: float = 0.7):
		super().__init__()
		self.total_steps = total_steps
		self.transition_point = transition_point
		self.structure_threshold = int(total_steps * transition_point)

	def get_current_phase(self, step: int) -> Dict[str, any]:
		"""Determine current refinement phase"""
		if step < self.structure_threshold:
			return {
				'phase': 'structure',
				'focus': 'high_level',
				'attention_pattern': 'global',
				'edit_granularity': 'coarse'
			}
		else:
			return {
				'phase': 'detail',
				'focus': 'local_coherence',
				'attention_pattern': 'local',
				'edit_granularity': 'fine'
			}

	def apply_phase_specific_attention(self, hidden_states: torch.Tensor,
									 phase_info: Dict[str, any]) -> torch.Tensor:
		"""Apply phase-specific attention patterns"""
		# This would modify attention patterns based on current phase
		# For now, return unchanged hidden states
		return hidden_states

class MultiScaleAttention(nn.Module):
	def __init__(self, num_scales: int, hidden_dim: int, num_heads: int):
		super().__init__()
		self.num_scales = num_scales
		self.hidden_dim = hidden_dim
		self.num_heads = num_heads

		# Separate attention heads for different scales
		self.scale_attentions = nn.ModuleList([
			AttentionSelector(hidden_dim, num_heads)
			for _ in range(num_scales)
		])

		# Scale combination network
		self.scale_combiner = nn.Linear(hidden_dim * num_scales, hidden_dim)

	def compute_hierarchical_attention(self, hidden_states: torch.Tensor,
									 scale_level: int) -> torch.Tensor:
		"""Compute attention at specified scale"""
		if scale_level < len(self.scale_attentions):
			attended_output, _ = self.scale_attentions[scale_level].compute_attention_weights(
				hidden_states, hidden_states, hidden_states
			)
			return attended_output
		else:
			return hidden_states

	def forward(self, hidden_states: torch.Tensor) -> torch.Tensor:
		"""Combine all scales"""
		scale_outputs = []

		for scale in range(self.num_scales):
			scale_output = self.compute_hierarchical_attention(hidden_states, scale)
			scale_outputs.append(scale_output)

		# Combine scales
		combined = torch.cat(scale_outputs, dim=-1)
		output = self.scale_combiner(combined)

		return output

# ============================================================================
# PHASE 5: CONSISTENCY AND QUALITY MONITORING
# ============================================================================

class ConsistencyChecker(nn.Module):
	def __init__(self, hidden_dim: int):
		super().__init__()
		self.hidden_dim = hidden_dim

		# Consistency scoring networks
		self.step_consistency_head = nn.Sequential(
			nn.Linear(hidden_dim * 2, hidden_dim),
			nn.ReLU(),
			nn.Linear(hidden_dim, 1),
			nn.Sigmoid()
		)

		self.semantic_coherence_head = nn.Sequential(
			nn.Linear(hidden_dim, hidden_dim // 2),
			nn.ReLU(),
			nn.Linear(hidden_dim // 2, 1),
			nn.Sigmoid()
		)

	def compute_step_consistency(self, prev_hidden: torch.Tensor,
							   curr_hidden: torch.Tensor) -> torch.Tensor:
		"""Compute consistency between consecutive diffusion steps"""
		# Cosine similarity
		prev_norm = F.normalize(prev_hidden, dim=-1)
		curr_norm = F.normalize(curr_hidden, dim=-1)
		cosine_sim = torch.sum(prev_norm * curr_norm, dim=-1)

		# Neural consistency score
		combined_hidden = torch.cat([prev_hidden, curr_hidden], dim=-1)
		neural_consistency = self.step_consistency_head(combined_hidden).squeeze(-1)

		# Combine both measures
		consistency_score = (cosine_sim + neural_consistency) / 2.0
		return consistency_score

	def compute_semantic_coherence(self, sequence_embeddings: torch.Tensor) -> torch.Tensor:
		"""Analyze semantic consistency across sequence"""
		batch_size, seq_len, hidden_dim = sequence_embeddings.shape

		# Pairwise coherence between adjacent tokens
		coherence_scores = []
		for i in range(seq_len - 1):
			curr_emb = sequence_embeddings[:, i, :]
			next_emb = sequence_embeddings[:, i + 1, :]

			combined = torch.cat([curr_emb, next_emb], dim=-1)
			coherence = self.step_consistency_head(combined).squeeze(-1)
			coherence_scores.append(coherence)

		# Average coherence across sequence
		avg_coherence = torch.stack(coherence_scores).mean(dim=0)
		return avg_coherence

class EarlyStoppingMonitor(nn.Module):
	def __init__(self, patience: int = 3, min_improvement: float = 0.01):
		super().__init__()
		self.patience = patience
		self.min_improvement = min_improvement

		# Buffers for tracking
		self.register_buffer('quality_history', torch.zeros(20))  # Last 20 steps
		self.register_buffer('history_idx', torch.tensor(0))
		self.register_buffer('best_quality', torch.tensor(float('-inf')))
		self.register_buffer('steps_without_improvement', torch.tensor(0))

	def should_stop(self, current_quality: torch.Tensor, current_step: int,
				   min_steps: int = 5) -> Tuple[bool, str]:
		"""Determine if early stopping should be triggered"""
		# Always continue for minimum steps
		if current_step < min_steps:
			return False, "minimum_steps_not_reached"

		# Update quality history
		self.update_quality_history(current_quality)

		# Check for improvement
		if current_quality > self.best_quality + self.min_improvement:
			self.best_quality = current_quality
			self.steps_without_improvement = 0
			return False, "improving"
		else:
			self.steps_without_improvement += 1

			if self.steps_without_improvement >= self.patience:
				return True, "no_improvement"

		# Check for quality degradation
		if len(self.get_recent_qualities()) >= 3:
			recent_qualities = self.get_recent_qualities()
			if torch.all(recent_qualities[1:] < recent_qualities[:-1]):
				return True, "quality_degradation"

		return False, "continuing"

	def update_quality_history(self, step_quality: torch.Tensor) -> None:
		"""Add current step quality to history"""
		idx = self.history_idx.item() % 20
		self.quality_history[idx] = step_quality
		self.history_idx += 1

	def get_recent_qualities(self, window: int = 5) -> torch.Tensor:
		"""Get recent quality scores"""
		if self.history_idx < window:
			return self.quality_history[:self.history_idx]
		else:
			start_idx = (self.history_idx - window) % 20
			if start_idx + window <= 20:
				return self.quality_history[start_idx:start_idx + window]
			else:
				return torch.cat([
					self.quality_history[start_idx:],
					self.quality_history[:window - (20 - start_idx)]
				])

class BranchManager(nn.Module):
	def __init__(self, max_branches: int = 3, hidden_dim: int = 768):
		super().__init__()
		self.max_branches = max_branches
		self.hidden_dim = hidden_dim

		# Branch scoring network
		self.branch_scorer = nn.Sequential(
			nn.Linear(hidden_dim, hidden_dim // 2),
			nn.ReLU(),
			nn.Linear(hidden_dim // 2, 1),
			nn.Sigmoid()
		)

		# Branch state storage
		self.active_branches = {}
		self.branch_scores = {}

	def create_branch(self, parent_state: Dict, branch_id: str,
					 branch_strategy: str) -> Dict:
		"""Create new diffusion branch from parent state"""
		if len(self.active_branches) >= self.max_branches:
			# Remove lowest scoring branch
			worst_branch = min(self.branch_scores.keys(),
							 key=lambda x: self.branch_scores[x])
			del self.active_branches[worst_branch]
			del self.branch_scores[worst_branch]

		# Create new branch
		new_branch = {
			'sequence': parent_state['sequence'].clone(),
			'hidden_states': parent_state['hidden_states'].clone(),
			'step': parent_state['step'],
			'strategy': branch_strategy,
			'parent_id': parent_state.get('branch_id', 'main')
		}

		self.active_branches[branch_id] = new_branch
		self.branch_scores[branch_id] = 0.5  # Initialize with neutral score

		return new_branch

	def evaluate_branches(self) -> Dict[str, float]:
		"""Score all active branches for quality and potential"""
		branch_scores = {}

		for branch_id, branch_state in self.active_branches.items():
			# Score based on hidden states
			hidden_mean = branch_state['hidden_states'].mean(dim=(0, 1))
			score = self.branch_scorer(hidden_mean).item()

			# Apply strategy-specific bonuses
			if branch_state['strategy'] == 'conservative':
				score *= 1.1  # Slight bonus for conservative approach
			elif branch_state['strategy'] == 'aggressive':
				score *= 0.9  # Slight penalty for aggressive approach

			branch_scores[branch_id] = score
			self.branch_scores[branch_id] = score

		return branch_scores

	def merge_or_select_branches(self) -> Tuple[str, Dict]:
		"""Select best branch or merge complementary branches"""
		if not self.active_branches:
			return None, None

		# Evaluate all branches
		scores = self.evaluate_branches()

		# Select best branch
		best_branch_id = max(scores.keys(), key=lambda x: scores[x])
		best_branch = self.active_branches[best_branch_id]

		return best_branch_id, best_branch
