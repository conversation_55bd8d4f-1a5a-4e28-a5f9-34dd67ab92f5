import logging
from dataclasses import dataclass
from pathlib import Path
from torch.utils.data import Dataset
from transformers import AutoTokenizer
from datasets import load_dataset
import hashlib
import re

@dataclass
class PretrainingDatasetConfig:
	dataset_name: str = "wikitext"
	dataset_variant: str = "wikitext-103-v1"
	max_length: int = 1024
	min_length: int = 50
	overlap_length: int = 128
	chunk_strategy: str = "sliding_window"
	tokenizer_name: str = "gpt2"
	min_words_per_chunk: int = 20
	max_words_per_chunk: int = 500
	filter_repetitive_text: bool = True
	max_repetition_ratio : int = 0.3
	min_unique_words_ratio: float = 0.3
	filter_non_english: bool = True
	min_alpha_ratio: float = 0.7
	num_workers: int = 4
	validation_split: float = 0.01
	test_split: float = 0.01
	shuffle_data: bool = True
	seed: int = 42
	deduplicate: bool = True
	normalize_whitespace: bool = True
	remove_markup: bool = True
	preserve_paragraphs: bool = True

	def __post_init__(self):
		assert self.max_length > self.min_length, "max_length must be greater than min_length"
		assert 0 <= self.overlap_length < self.max_length, "overlap_length must be valid"
		assert 0.0 <= self.validation_split <= 1.0, "validation_split must be between 0 and 1"
		assert 0.0 <= self.test_split <= 1.0, "test_split must be between 0 and 1"
		assert (self.validation_split + self.test_split) < 1.0, "splits must sum to less than 1"
		assert self.chunk_strategy in ["sliding_window", "sentence_boundary", "paragraph"], "Invalid chunk strategy"

class TextProcessor:
	def __init__(self, config: PretrainingDatasetConfig):
		self.config = config
		self.logger = logging.getLogger(f"{__name__}.TextProcessor")
		self._compile_patterns()
		self.processing_stats = {
			"total_chunks": 0,
			"filtered_short": 0,
			"filtered_repetitive": 0,
			"filtered_non_english": 0,
			"filtered_low_alpha": 0,
			"processed_chunks": 0
		}

	def _compile_patterns(self):
		self.html_pattern = re.compile(r'<[^>]+>')
		self.whitespace_pattern = re.compile(r'\s+')
		self.non_alpha_pattern = re.compile(r'[^a-zA-Z\s]')
		self.sentence_boundary_pattern = re.compile(r'[.!?]+\s+')
		self.paragraph_pattern = re.compile(r'\n\s*\n')

	def process_text(self, text: str) -> str | None:
		if not text or not text.strip():
			return None

		if self.config.remove_markup:
			text = self._remove_markup(text)

		if self.config.normalize_whitespace:
			text = self._normalize_whitespace(text)

		if not self._passes_quality_filters(text):
			return None

		return text.strip()

	def _remove_markup(self, text: str) -> str:
		text = self.html_pattern.sub(' ', text)
		text = re.sub(r'\{\{[^}]*\}\}', '', text)
		text = re.sub(r'\[\[[^\]]*\]\]', '', text)
		text = re.sub(r'<ref[^>]*>.*?</ref>', '', text, flags=re.DOTALL)
		return text

	def _normalize_whitespace(self, text: str) -> str:
		if self.config.preserve_paragraphs:
			paragraphs = self.paragraph_pattern.split(text)
			normalized_paragraphs = []
			for para in paragraphs:
				normalized = self.whitespace_pattern.sub(' ', para.strip())
				if normalized:
					normalized_paragraphs.append(normalized)
			return '\n\n'.join(normalized_paragraphs)
		else:
			return self.whitespace_pattern.sub(' ', text).strip()

	def _passes_quality_filters(self, text: str) -> bool:
		self.processing_stats["total_chunks"] += 1

		words = text.split()
		if len(words) < self.config.min_words_per_chunk or len(words) > self.config.max_words_per_chunk:
			self.processing_stats["filtered_short"] += 1
			return False

		if self.config.filter_repetitive_text and self._is_repetitive(text):
			self.processing_stats["filtered_repetitive"] += 1
			return False

		if self.config.filter_non_english and not self._is_likely_english(text):
			self.processing_stats["filtered_non_english"] += 1
			return False

		alpha_ratio = len(self.non_alpha_pattern.sub('', text)) / max(len(text), 1)
		if alpha_ratio < self.config.min_alpha_ratio:
			self.processing_stats["filtered_low_alpha"] += 1
			return False

		unique_words = set(words)
		unique_ratio = len(unique_words) / max(len(words), 1)
		if unique_ratio < self.config.min_unique_words_ratio:
			self.processing_stats["filtered_repetitive"] += 1
			return False

		self.processing_stats["processed_chunks"] += 1
		return True

	def _is_repetitive(self, text: str) -> bool:
		words = text.split()
		if len(words) < 10:
			return False
		trigrams = [' '.join(words[i:i+3]) for i in range(len(words)-2)]
		unique_trigrams = set(trigrams)
		repetition_ratio = 1.0 - (len(unique_trigrams) / max(len(trigrams), 1))
		return repetition_ratio > self.config.max_repetition_ratio

	def _is_likely_english(self, text: str) -> bool:
		common_english = {'the', 'and', 'to', 'of', 'a', 'in', 'is', 'it', 'you', 'that', 'he', 'was', 'for', 'on', 'are', 'as', 'with', 'his', 'they', 'i'}
		words = set(word.lower() for word in text.split())
		overlap = len(words.intersection(common_english))
		return overlap >= 3

class TextChunker:
	def __init__(self, config: PretrainingDatasetConfig, tokenizer):
		self.config = config
		self.tokenizer = tokenizer
		self.logger = logging.getLogger(f"{__name__}.TextChunker")
		self.chunk_strategies = {
			"sliding_window": self._sliding_window_chunking,
			"sentence_boundary": self._sentence_boundary_chunking,
			"paragraph": self._paragraph_chunking
		}

	def chunk_text(self, text: str) -> list[str]:
		strategy = self.chunk_strategies.get(self.config.chunk_strategy)
		if not strategy:
			raise ValueError(f"Unknown chunking strategy: {self.config.chunk_strategy}")
		return strategy(text)

	def _sliding_window_chunking(self, text: str) -> list[str]:
		tokens = self.tokenizer.encode(text, add_special_tokens=False)
		if len(tokens) <= self.config.max_length:
			return [text]
		chunks = []
		start_idx = 0
		while start_idx < len(tokens):
			end_idx = min(start_idx + self.config.max_length, len(tokens))
			chunk_tokens = tokens[start_idx:end_idx]
			chunk_text = self.tokenizer.decode(chunk_tokens, skip_special_tokens=True)
			if len(chunk_text.strip()) >= self.config.min_length:
				chunks.append(chunk_text)
			if end_idx >= len(tokens):
				break
			start_idx = end_idx - self.config.overlap_length
		return chunks

	def _sentence_boundary_chunking(self, text: str) -> list[str]:
		sentences = re.split(r'[.!?]+\s+', text)
		chunks = []
		current_chunk = ""
		current_tokens = 0
		for sentence in sentences:
			sentence = sentence.strip()
			if not sentence:
				continue
			sentence_tokens = self.tokenizer.encode(sentence, add_special_tokens=False)
			if len(sentence_tokens) > self.config.max_length:
				if current_chunk:
					chunks.append(current_chunk.strip())
					current_chunk = ""
					current_tokens = 0
				sentence_chunks = self._sliding_window_chunking(sentence)
				chunks.extend(sentence_chunks)
				continue
			if current_tokens + len(sentence_tokens) > self.config.max_length:
				if current_chunk and len(current_chunk.strip()) >= self.config.min_length:
					chunks.append(current_chunk.strip())
				current_chunk = sentence
				current_tokens = len(sentence_tokens)
			else:
				current_chunk += " " + sentence if current_chunk else sentence
				current_tokens += len(sentence_tokens)
		if current_chunk and len(current_chunk.strip()) >= self.config.min_length:
			chunks.append(current_chunk.strip())
		return chunks

	def _paragraph_chunking(self, text: str) -> list[str]:
		paragraphs = re.split(r'\n\s*\n', text)
		chunks = []
		current_chunk = ""
		current_tokens = 0
		for paragraph in paragraphs:
			paragraph = paragraph.strip()
			if not paragraph:
				continue
			paragraph_tokens = self.tokenizer.encode(paragraph, add_special_tokens=False)
			if len(paragraph_tokens) > self.config.max_length:
				if current_chunk:
					chunks.append(current_chunk.strip())
					current_chunk = ""
					current_tokens = 0
				paragraph_chunks = self._sentence_boundary_chunking(paragraph)
				chunks.extend(paragraph_chunks)
				continue
			if current_tokens + len(paragraph_tokens) > self.config.max_length:
				if current_chunk and len(current_chunk.strip()) >= self.config.min_length:
					chunks.append(current_chunk.strip())
				current_chunk = paragraph
				current_tokens = len(paragraph_tokens)
			else:
				current_chunk += "\n\n" + paragraph if current_chunk else paragraph
				current_tokens += len(paragraph_tokens)
		if current_chunk and len(current_chunk.strip()) >= self.config.min_length:
			chunks.append(current_chunk.strip())
		return chunks

class PretrainingDataset(Dataset):
	def __init__(self, config: PretrainingDatasetConfig, split: str = "train"):
		self.config = config
		self.split = split
		self.logger = logging.getLogger(f"{__name__}.PretrainingDataset")
		self.tokenizer = AutoTokenizer.from_pretrained(config.tokenizer_name)
		if self.tokenizer.pad_token is None:
			self.tokenizer.pad_token = self.tokenizer.eos_token
		self.text_processor = TextProcessor(config)
		self.text_chunker = TextChunker(config, self.tokenizer)
		self.dataset = self._load_and_process_dataset()
		self.logger.info(f"Loaded {len(self.dataset)} examples for {split} split")

	def _load_and_process_dataset(self):
		try:
			hf_dataset = load_dataset(self.config.dataset_name, self.config.dataset_variant, split=self.split)
		except ValueError:
			full_dataset = load_dataset(self.config.dataset_name, self.config.dataset_variant)
			if "train" in full_dataset:
				full_dataset = full_dataset["train"]
			else:
				full_dataset = list(full_dataset.values())[0]
			if self.split == "train":
				hf_dataset = full_dataset.train_test_split(test_size=self.config.validation_split + self.config.test_split)["train"]
			elif self.split == "validation":
				temp = full_dataset.train_test_split(test_size=self.config.validation_split + self.config.test_split)
				hf_dataset = temp["test"].train_test_split(test_size=self.config.test_split / (self.config.validation_split + self.config.test_split))["train"]
			elif self.split == "test":
				temp = full_dataset.train_test_split(test_size=self.config.validation_split + self.config.test_split)
				hf_dataset = temp["test"].train_test_split(test_size=self.config.test_split / (self.config.validation_split + self.config.test_split))["test"]
			else:
				raise ValueError(f"Invalid split: {self.split}")

		def process_and_chunk_batch(batch):
			texts = batch["text"]
			all_chunks = []
			for text in texts:
				processed = self.text_processor.process_text(text)
				if processed:
					chunks = self.text_chunker.chunk_text(processed)
					all_chunks.extend([{"text": chunk} for chunk in chunks])
			return {"text": [chunk["text"] for chunk in all_chunks]}

		processed_dataset = hf_dataset.map(
			process_and_chunk_batch,
			batched=True,
			remove_columns=hf_dataset.column_names,
			num_proc=self.config.num_workers
		)

		def add_metadata(example):
			return {
				"text": example["text"],
				"source": f"{self.config.dataset_name}/{self.config.dataset_variant}",
				"split": self.split
			}

		final_dataset = processed_dataset.map(add_metadata, num_proc=self.config.num_workers)

		if self.config.deduplicate:
			def add_hash(example):
				example["hash"] = hashlib.md5(example["text"].encode()).hexdigest()
				return example
			final_dataset = final_dataset.map(add_hash, num_proc=self.config.num_workers)

		return final_dataset

	def save_to_jsonl(self, output_path: Path) -> None:
		output_path.parent.mkdir(parents=True, exist_ok=True)
		self.dataset.to_json(output_path, orient="records", lines=True)
		self.logger.info(f"Saved {len(self.dataset)} examples to {output_path}")

	def __len__(self) -> int:
		return len(self.dataset)

	def __getitem__(self, idx):
		return self.dataset[idx]

class PretrainingDatasetFactory:
	@staticmethod
	def create_dataset_files(config: PretrainingDatasetConfig, output_dir: str) -> dict[str, Path]:
		output_path = Path(output_dir)
		output_path.mkdir(parents=True, exist_ok=True)
		created_files = {}
		for split in ["train", "validation"]:
			logger = logging.getLogger(f"{__name__}.Factory")
			logger.info(f"Creating {split} dataset...")
			dataset = PretrainingDataset(config, split=split)
			output_file = output_path / f"pretraining_{split}.jsonl"
			dataset.save_to_jsonl(output_file)
			created_files[split] = output_file
		return created_files

def create_pretraining_datasets():
	logging.basicConfig(
		level=logging.INFO,
		format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
		handlers=[logging.FileHandler('pretraining_dataset.log'), logging.StreamHandler()]
	)
	logger = logging.getLogger(__name__)
	logger.info("Starting pretraining dataset creation...")

	config = PretrainingDatasetConfig(
		dataset_name="wikitext",
		dataset_variant="wikitext-103-v1",
		tokenizer_name="gpt2",
		max_length=1024,
		min_length=50,
		overlap_length=128,
		chunk_strategy="sliding_window",
		num_workers=4,
		validation_split=0.01,
		deduplicate=True
	)

	try:
		output_files = PretrainingDatasetFactory.create_dataset_files(config, output_dir="./data")
		logger.info("Successfully created pretraining datasets:")
		for split, filepath in output_files.items():
			logger.info(f"  {split}: {filepath}")
	except Exception as e:
		logger.error(f"Dataset creation failed: {str(e)}")
		raise

if __name__ == "__main__":
	create_pretraining_datasets()
