
from collections import defaultdict
from dataclasses import dataclass, field
from pathlib import Path
from torch.utils.data import Dataset, DataLoader
from typing import Dict, List, Optional, Tuple, Any

import aiohttp
import asyncio
import json
import logging
import random
import re
import time
import torch
import torch.nn as nn
import torch.nn.functional as F
import wandb

# Import our existing components
from model import DiffusionConfig
from train import TrainingConfig, DiffusionTrainer, DiffusionLoss, save_checkpoint, validate_inputs

# ============================================================================
# FUNDAMENTAL COMPONENT 1: PARENT MODEL INTERFACE
# ============================================================================

@dataclass
class ParentModelConfig:
	"""Configuration for the parent model (DeepSeek-R1)"""
	model_name: str = "DeepSeek-R1-0528-Qwen3-8B-BF16:latest"
	ollama_host: str = "http://localhost:11434"
	max_tokens: int = 2048
	temperature: float = 0.7
	top_p: float = 0.9
	timeout: int = 120
	max_retries: int = 3
	concurrent_requests: int = 4

	# Generation parameters for different types of tasks
	reasoning_temperature: float = 0.8  # Higher for creative reasoning
	factual_temperature: float = 0.3    # Lower for factual responses
	creative_temperature: float = 1.0   # Highest for creative tasks

class OllamaClient:
	"""
	Asynchronous client for communicating with Ollama DeepSeek-R1 model.

	This class handles:
	- Connection management to local Ollama instance
	- Request/response parsing with proper error handling
	- Rate limiting and concurrent request management
	- Automatic retry logic for failed requests
	"""

	def __init__(self, config: ParentModelConfig):
		self.config = config
		self.session: Optional[aiohttp.ClientSession] = None
		self.request_semaphore = asyncio.Semaphore(config.concurrent_requests)
		self.request_count = 0
		self.successful_requests = 0
		self.failed_requests = 0

		# Setup logging
		self.logger = logging.getLogger(f"{__name__}.OllamaClient")

	async def __aenter__(self):
		"""Async context manager entry"""
		connector = aiohttp.TCPConnector(limit=20, limit_per_host=10)
		timeout = aiohttp.ClientTimeout(total=self.config.timeout)
		self.session = aiohttp.ClientSession(connector=connector, timeout=timeout)

		# Test connection
		await self._test_connection()
		return self

	async def __aexit__(self, exc_type, exc_val, exc_tb):
		"""Async context manager exit"""
		if self.session:
			await self.session.close()

	async def _test_connection(self) -> None:
		"""Test connection to Ollama and verify model availability"""
		try:
			# Test basic connection
			async with self.session.get(f"{self.config.ollama_host}/api/tags") as response:
				if response.status != 200:
					raise ConnectionError(f"Ollama not accessible: HTTP {response.status}")

				models = await response.json()
				model_names = [model["name"] for model in models.get("models", [])]

				if self.config.model_name not in model_names:
					self.logger.warning(
						f"Model {self.config.model_name} not found. Available models: {model_names}"
					)

			self.logger.info(f"Successfully connected to Ollama at {self.config.ollama_host}")

		except Exception as e:
			raise ConnectionError(f"Failed to connect to Ollama: {str(e)}")

	async def generate_response(self, prompt: str,
							  task_type: str = "general",
							  system_prompt: Optional[str] = None) -> Dict[str, Any]:
		"""
		Generate response from DeepSeek-R1 with task-specific parameters.

		Args:
			prompt: The input prompt for the model
			task_type: Type of task (reasoning, factual, creative, general)
			system_prompt: Optional system prompt to guide behavior

		Returns:
			Dict containing response, thoughts, and metadata
		"""
		async with self.request_semaphore:
			for attempt in range(self.config.max_retries):
				try:
					# Select temperature based on task type
					temperature = {
						"reasoning": self.config.reasoning_temperature,
						"factual": self.config.factual_temperature,
						"creative": self.config.creative_temperature,
						"general": self.config.temperature
					}.get(task_type, self.config.temperature)

					# Prepare request payload
					payload = {
						"model": self.config.model_name,
						"prompt": prompt,
						"system": system_prompt or "",
						"stream": False,
						"options": {
							"temperature": temperature,
							"top_p": self.config.top_p,
							"num_predict": self.config.max_tokens,
						}
					}

					start_time = time.time()

					async with self.session.post(
						f"{self.config.ollama_host}/api/generate",
						json=payload
					) as response:

						if response.status != 200:
							error_text = await response.text()
							raise aiohttp.ClientError(f"HTTP {response.status}: {error_text}")

						result = await response.json()
						generation_time = time.time() - start_time

						# Parse the response
						parsed_response = self._parse_deepseek_response(result["response"])

						self.request_count += 1
						self.successful_requests += 1

						return {
							"raw_response": result["response"],
							"thoughts": parsed_response["thoughts"],
							"output": parsed_response["output"],
							"task_type": task_type,
							"temperature": temperature,
							"generation_time": generation_time,
							"tokens_generated": len(result["response"].split()),
							"model": self.config.model_name,
							"attempt": attempt + 1
						}

				except Exception as e:
					self.failed_requests += 1
					self.logger.warning(f"Request attempt {attempt + 1} failed: {str(e)}")

					if attempt == self.config.max_retries - 1:
						raise

					# Exponential backoff
					await asyncio.sleep(2 ** attempt)

		raise RuntimeError("All retry attempts exhausted")

	def _parse_deepseek_response(self, response: str) -> Dict[str, str]:
		"""
		Parse DeepSeek-R1 response to extract thoughts and output.

		The model generates responses in the format:
		<think>reasoning process here</think>
		Final output here
		"""
		thoughts = ""
		output = response

		# Extract thoughts using regex
		think_pattern = r"<think>(.*?)</think>"
		think_matches = re.findall(think_pattern, response, re.DOTALL)

		if think_matches:
			# Join all thinking sections
			thoughts = "\n".join(match.strip() for match in think_matches)

			# Remove think tags from output
			output = re.sub(think_pattern, "", response, flags=re.DOTALL).strip()

		return {
			"thoughts": thoughts,
			"output": output
		}

	def get_statistics(self) -> Dict[str, Any]:
		"""Get client usage statistics"""
		success_rate = self.successful_requests / max(self.request_count, 1)
		return {
			"total_requests": self.request_count,
			"successful_requests": self.successful_requests,
			"failed_requests": self.failed_requests,
			"success_rate": success_rate
		}

# ============================================================================
# FUNDAMENTAL COMPONENT 2: DATA GENERATION PIPELINE
# ============================================================================

@dataclass
class DataGenerationConfig:
	"""Configuration for training data generation"""
	num_examples_per_category: int = 1000
	max_concurrent_generations: int = 4
	output_dir: str = "./generated_data"
	save_interval: int = 100

	# Task distribution weights
	reasoning_weight: float = 0.3
	factual_weight: float = 0.2
	creative_weight: float = 0.2
	instruction_weight: float = 0.2
	conversation_weight: float = 0.1

	# Quality filtering
	min_thought_length: int = 50  # Minimum characters in thoughts
	min_output_length: int = 20   # Minimum characters in output
	max_output_length: int = 1000 # Maximum characters in output

class TaskGenerator:
	"""
	Generates diverse prompts for training the child model.

	This class creates various types of tasks that encourage
	the parent model to demonstrate different reasoning patterns
	and knowledge domains.
	"""

	def __init__(self):
		self.reasoning_prompts = [
			"Solve this step by step: {problem}",
			"Analyze the following situation and provide your reasoning: {scenario}",
			"Compare and contrast these concepts, explaining your thought process: {concepts}",
			"What would happen if {hypothetical}? Think through the implications.",
			"Explain why {statement} is true or false, showing your reasoning.",
		]

		self.factual_prompts = [
			"Explain the concept of {topic} in detail.",
			"What are the key facts about {subject}?",
			"Describe the process of {process}.",
			"List and explain the main features of {item}.",
			"What is the relationship between {concept_a} and {concept_b}?",
		]

		self.creative_prompts = [
			"Write a short story about {theme}.",
			"Create a dialogue between {character_a} and {character_b} discussing {topic}.",
			"Imagine you are {role}. How would you approach {situation}?",
			"Generate creative solutions for {problem}.",
			"Describe {scene} in vivid, creative language.",
		]

		self.instruction_prompts = [
			"How do I {task}? Provide step-by-step instructions.",
			"What's the best way to {goal}?",
			"Can you help me understand {concept}?",
			"I need advice on {situation}. What should I do?",
			"Explain how to {skill} for a beginner.",
		]

		# Sample content for filling templates
		self.content_samples = {
			"problems": [
				"A train travels at 80 mph for 2 hours, then at 60 mph for 3 hours. What's the average speed?",
				"If 3x + 7 = 22, what is the value of x?",
				"A company's profit increases by 15% each year. If they made $100,000 this year, what will they make in 3 years?",
			],
			"scenarios": [
				"A team project is behind schedule and team members are blaming each other",
				"You find a wallet on the street with cash and ID",
				"Your friend asks to borrow money but has never paid you back before",
			],
			"concepts": [
				"democracy and authoritarianism",
				"artificial intelligence and human intelligence",
				"renewable energy and fossil fuels",
			],
			"topics": [
				"quantum computing", "photosynthesis", "blockchain technology",
				"climate change", "machine learning", "supply chain management",
			],
			"themes": [
				"time travel", "first contact with aliens", "a world without internet",
				"life in a floating city", "artificial intelligence gaining consciousness",
			],
			"tasks": [
				"start a vegetable garden", "learn a new language", "improve my sleep quality",
				"organize my digital files", "build better habits",
			]
		}

	def generate_prompt(self, task_type: str) -> Tuple[str, str]:
		"""
		Generate a prompt of the specified type.

		Returns:
			Tuple of (prompt, expected_task_type)
		"""
		try:
			if task_type == "reasoning":
				template = random.choice(self.reasoning_prompts)

				# Create a mapping for all possible placeholders
				placeholder_map = {
					"problem": random.choice(self.content_samples["problems"]),
					"scenario": random.choice(self.content_samples["scenarios"]),
					"concepts": random.choice(self.content_samples["concepts"]),
					"hypothetical": "social media suddenly disappeared",
					"statement": "exercise improves mental health"
				}

				# Use safe string formatting
				prompt = self._safe_format(template, placeholder_map)

			elif task_type == "factual":
				template = random.choice(self.factual_prompts)
				topic1 = random.choice(self.content_samples["topics"])
				topic2 = random.choice(self.content_samples["topics"])

				placeholder_map = {
					"topic": topic1,
					"subject": topic1,
					"process": topic1,
					"item": topic1,
					"concept_a": topic1,
					"concept_b": topic2
				}

				prompt = self._safe_format(template, placeholder_map)

			elif task_type == "creative":
				template = random.choice(self.creative_prompts)

				placeholder_map = {
					"theme": random.choice(self.content_samples["themes"]),
					"character_a": "a scientist",
					"character_b": "an artist",
					"topic": random.choice(self.content_samples["topics"]),
					"role": "a detective",
					"situation": "a mysterious disappearance",
					"problem": "reducing plastic waste",
					"scene": "a bustling marketplace at sunset"
				}

				prompt = self._safe_format(template, placeholder_map)

			elif task_type == "instruction":
				template = random.choice(self.instruction_prompts)
				task_content = random.choice(self.content_samples["tasks"])

				placeholder_map = {
					"task": task_content,
					"goal": task_content,
					"concept": random.choice(self.content_samples["topics"]),
					"situation": "career change",
					"skill": "public speaking"
				}

				prompt = self._safe_format(template, placeholder_map)

			else:  # conversation
				conversation_starters = [
					"I'm feeling overwhelmed with work lately. Any suggestions?",
					"What do you think about the impact of AI on jobs?",
					"I'm trying to decide between two career paths. Can you help me think through it?",
					"How do you stay motivated when working on long-term goals?",
				]
				prompt = random.choice(conversation_starters)

			return prompt, task_type

		except Exception as e:
			# Fallback to simple prompt if formatting fails
			return f"Please help me understand {random.choice(self.content_samples['topics'])}.", task_type

	def _safe_format(self, template: str, placeholder_map: Dict[str, str]) -> str:
		"""Safely format template with placeholders, handling missing keys"""
		try:
			# First, find all placeholders in the template
			import re
			placeholders = re.findall(r'\{(\w+)\}', template)

			# Create a complete mapping with defaults for missing keys
			complete_map = {}
			for placeholder in placeholders:
				if placeholder in placeholder_map:
					complete_map[placeholder] = placeholder_map[placeholder]
				else:
					# Provide sensible defaults
					complete_map[placeholder] = f"[{placeholder}]"

			return template.format(**complete_map)

		except Exception:
			# If all else fails, return template with placeholders removed
			return re.sub(r'\{[^}]*\}', '[content]', template)

class DataGenerator:
	"""
	Orchestrates the generation of training data using the parent model.

	This class manages the entire pipeline from prompt generation
	to data validation and storage.
	"""

	def __init__(self, parent_config: ParentModelConfig,
				 generation_config: DataGenerationConfig):
		self.parent_config = parent_config
		self.generation_config = generation_config
		self.task_generator = TaskGenerator()

		# Statistics tracking
		self.generation_stats = defaultdict(int)
		self.quality_stats = defaultdict(int)

		# Setup logging
		self.logger = logging.getLogger(f"{__name__}.DataGenerator")

		# Create output directory
		Path(self.generation_config.output_dir).mkdir(parents=True, exist_ok=True)

	async def generate_training_data(self) -> List[Dict[str, Any]]:
		"""
		Generate comprehensive training dataset using the parent model.

		Returns:
			List of training examples with thoughts and outputs
		"""
		self.logger.info("Starting training data generation...")

		# Calculate task distribution
		task_weights = {
			"reasoning": self.generation_config.reasoning_weight,
			"factual": self.generation_config.factual_weight,
			"creative": self.generation_config.creative_weight,
			"instruction": self.generation_config.instruction_weight,
			"conversation": self.generation_config.conversation_weight,
		}

		# Generate task list
		tasks = []
		total_examples = self.generation_config.num_examples_per_category

		for task_type, weight in task_weights.items():
			num_tasks = int(total_examples * weight)
			tasks.extend([(task_type, i) for i in range(num_tasks)])

		random.shuffle(tasks)

		# Generate data in batches
		generated_data = []

		async with OllamaClient(self.parent_config) as client:
			# Process in batches for better progress tracking
			batch_size = self.generation_config.max_concurrent_generations

			for i in range(0, len(tasks), batch_size):
				batch_tasks = tasks[i:i + batch_size]

				# Generate batch concurrently
				batch_results = await asyncio.gather(
					*[self._generate_single_example(client, task_type, task_id)
					  for task_type, task_id in batch_tasks],
					return_exceptions=True
				)

				# Process results
				for result in batch_results:
					if isinstance(result, Exception):
						self.logger.warning(f"Generation failed: {result}")
						self.generation_stats["failed"] += 1
					elif self._validate_example(result):
						generated_data.append(result)
						self.generation_stats["successful"] += 1
						self.quality_stats[result["task_type"]] += 1
					else:
						self.generation_stats["rejected"] += 1

				# Save intermediate results
				if len(generated_data) % self.generation_config.save_interval == 0:
					await self._save_intermediate_data(generated_data)

				# Progress update
				self.logger.info(
					f"Generated {len(generated_data)} examples "
					f"({len(generated_data) / len(tasks) * 100:.1f}%)"
				)

		# Final save
		await self._save_final_data(generated_data)

		# Log statistics
		client_stats = client.get_statistics()
		self.logger.info(f"Generation complete. Statistics: {self.generation_stats}")
		self.logger.info(f"Client statistics: {client_stats}")
		self.logger.info(f"Quality distribution: {dict(self.quality_stats)}")

		return generated_data

	async def _generate_single_example(self, client: OllamaClient,
									 task_type: str, task_id: int) -> Dict[str, Any]:
		"""Generate a single training example"""
		prompt, _ = self.task_generator.generate_prompt(task_type)

		# Add system prompt to encourage thinking
		system_prompt = (
			"You are an AI assistant that thinks step by step. "
			"Before providing your final answer, use <think>...</think> tags "
			"to show your reasoning process. Be thorough in your thinking."
		)

		response = await client.generate_response(
			prompt=prompt,
			task_type=task_type,
			system_prompt=system_prompt
		)

		return {
			"id": f"{task_type}_{task_id}",
			"prompt": prompt,
			"system_prompt": system_prompt,
			"thoughts": response["thoughts"],
			"output": response["output"],
			"task_type": task_type,
			"generation_metadata": {
				"temperature": response["temperature"],
				"generation_time": response["generation_time"],
				"tokens_generated": response["tokens_generated"],
				"model": response["model"]
			}
		}

	def _validate_example(self, example: Dict[str, Any]) -> bool:
		"""Validate the quality of a generated example"""
		thoughts = example.get("thoughts", "")
		output = example.get("output", "")

		# Length checks
		if len(thoughts) < self.generation_config.min_thought_length:
			return False

		if len(output) < self.generation_config.min_output_length:
			return False

		if len(output) > self.generation_config.max_output_length:
			return False

		# Content quality checks
		if not thoughts.strip() or not output.strip():
			return False

		# Avoid repetitive or low-quality content
		if len(set(thoughts.split())) < len(thoughts.split()) * 0.5:
			return False  # Too repetitive

		return True

	async def _save_intermediate_data(self, data: List[Dict[str, Any]]) -> None:
		"""Save intermediate data to prevent loss"""
		timestamp = int(time.time())
		filepath = Path(self.generation_config.output_dir) / f"intermediate_{timestamp}.jsonl"

		with open(filepath, 'w') as f:
			for example in data[-self.generation_config.save_interval:]:
				f.write(json.dumps(example) + '\n')

	async def _save_final_data(self, data: List[Dict[str, Any]]) -> None:
		"""Save final training data"""
		filepath = Path(self.generation_config.output_dir) / "training_data.jsonl"

		with open(filepath, 'w') as f:
			for example in data:
				f.write(json.dumps(example) + '\n')

		self.logger.info(f"Saved {len(data)} examples to {filepath}")

# ============================================================================
# FUNDAMENTAL COMPONENT 3: THOUGHT PROCESS EXTRACTION
# ============================================================================

class ThoughtProcessor:
	"""
	Processes and analyzes the thought patterns from the parent model.

	This component extracts structured reasoning patterns that can be
	used to train the child model's internal reasoning capabilities.
	"""

	def __init__(self):
		self.reasoning_patterns = {
			"step_by_step": r"(?:first|then|next|finally|step \d+)",
			"cause_effect": r"(?:because|therefore|as a result|consequently)",
			"comparison": r"(?:compared to|unlike|similar to|on the other hand)",
			"evidence": r"(?:for example|evidence shows|studies indicate)",
			"uncertainty": r"(?:might|could|possibly|unclear|uncertain)",
			"conclusion": r"(?:in conclusion|therefore|overall|to summarize)"
		}

	def extract_reasoning_structure(self, thoughts: str) -> Dict[str, Any]:
		"""
		Extract structured reasoning patterns from thought text.

		Args:
			thoughts: Raw thought text from parent model

		Returns:
			Dict containing reasoning structure analysis
		"""
		structure = {
			"raw_thoughts": thoughts,
			"sentence_count": len([s for s in thoughts.split('.') if s.strip()]),
			"reasoning_patterns": {},
			"complexity_score": 0.0,
			"reasoning_steps": [],
			"key_concepts": []
		}

		# Analyze reasoning patterns
		for pattern_name, pattern_regex in self.reasoning_patterns.items():
			matches = re.findall(pattern_regex, thoughts, re.IGNORECASE)
			structure["reasoning_patterns"][pattern_name] = len(matches)

		# Extract reasoning steps
		structure["reasoning_steps"] = self._extract_reasoning_steps(thoughts)

		# Calculate complexity score
		structure["complexity_score"] = self._calculate_complexity(structure)

		# Extract key concepts (simple keyword extraction)
		structure["key_concepts"] = self._extract_key_concepts(thoughts)

		return structure

	def _extract_reasoning_steps(self, thoughts: str) -> List[str]:
		"""Extract individual reasoning steps from thoughts"""
		# Split by common step indicators
		step_indicators = [
			r"First,", r"Second,", r"Third,", r"Next,", r"Then,", r"Finally,",
			r"Step \d+:", r"\d+\.", r"•", r"-"
		]

		steps = []
		lines = thoughts.split('\n')

		for line in lines:
			line = line.strip()
			if not line:
				continue

			# Check if line starts with a step indicator
			is_step = any(re.match(indicator, line, re.IGNORECASE) for indicator in step_indicators)

			if is_step or len(line) > 30:  # Assume substantial lines are steps
				steps.append(line)

		return steps[:10]  # Limit to 10 steps for practicality

	def _calculate_complexity(self, structure: Dict[str, Any]) -> float:
		"""Calculate reasoning complexity score"""
		base_score = structure["sentence_count"] * 0.1

		# Add points for different reasoning patterns
		pattern_bonus = sum(structure["reasoning_patterns"].values()) * 0.2

		# Add points for reasoning steps
		step_bonus = len(structure["reasoning_steps"]) * 0.1

		complexity = base_score + pattern_bonus + step_bonus
		return min(complexity, 10.0)  # Cap at 10

	def _extract_key_concepts(self, thoughts: str) -> List[str]:
		"""Extract key concepts using simple heuristics"""
		# Remove common words and extract meaningful terms
		# Simple approach: find capitalized words and technical terms
		words = re.findall(r'\b[A-Z][a-z]+\b|\b[a-z]+(?:ing|tion|ness|ment)\b', thoughts)

		# Filter and deduplicate
		concepts = []
		common_words = {'The', 'This', 'That', 'When', 'Where', 'What', 'How', 'Why'}

		for word in words:
			if word not in common_words and len(word) > 3:
				concepts.append(word.lower())

		return list(set(concepts))[:10]  # Return top 10 unique concepts

# ============================================================================
# FUNDAMENTAL COMPONENT 4: KNOWLEDGE DISTILLATION FRAMEWORK
# ============================================================================

class KnowledgeDistillationLoss(nn.Module):
	"""
	Knowledge distillation loss for training child model with parent guidance.

	This loss function combines multiple objectives:
	1. Traditional cross-entropy loss for text generation
	2. Thought alignment loss to match reasoning patterns
	3. Output alignment loss to match final responses
	4. Confidence matching to transfer uncertainty modeling
	"""

	def __init__(self, alpha: float = 0.5, beta: float = 0.3, gamma: float = 0.2,
				 temperature: float = 3.0, hidden_dim: int = 768):
		super().__init__()
		self.alpha = alpha  # Weight for generation loss
		self.beta = beta    # Weight for thought alignment
		self.gamma = gamma  # Weight for output alignment
		self.temperature = temperature  # Distillation temperature

		# Initialize projection layers for dimension alignment
		self.hidden_dim = hidden_dim
		self.thought_projector = None  # Will be initialized when needed
		self.confidence_projector = nn.Linear(1, 1)  # Simple confidence scaling

		# Initialize buffers for teacher data storage
		self.register_buffer('teacher_logits_available', torch.tensor(False))

	def _init_thought_projector(self, student_dim: int, teacher_dim: int):
		"""Initialize thought projection layer with proper dimensions"""
		if self.thought_projector is None or self.thought_projector.in_features != student_dim:
			self.thought_projector = nn.Linear(student_dim, teacher_dim).to(
				next(self.parameters()).device
			)
			# Proper initialization
			nn.init.xavier_uniform_(self.thought_projector.weight)
			nn.init.zeros_(self.thought_projector.bias)

	def forward(self, student_outputs: Dict[str, torch.Tensor],
				teacher_data: Dict[str, Any],
				targets: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
		"""
		Compute knowledge distillation loss.

		Args:
			student_outputs: Outputs from diffusion model
			teacher_data: Processed data from parent model
			targets: Ground truth targets

		Returns:
			Dict of loss components
		"""
		losses = {}
		device = next(self.parameters()).device

		# 1. Standard generation loss
		student_logits = student_outputs["logits"]
		labels = targets["labels"]

		# Ensure tensors are on correct device
		labels = labels.to(device)

		generation_loss = F.cross_entropy(
			student_logits.view(-1, student_logits.size(-1)),
			labels.view(-1),
			ignore_index=-100,
			reduction='mean'
		)
		losses["generation"] = generation_loss

		# 2. Thought alignment loss (if thoughts are available)
		if "thought_embeddings" in teacher_data and "hidden_states" in student_outputs:
			try:
				thought_loss = self._compute_thought_alignment_loss(
					student_outputs["hidden_states"],
					teacher_data["thought_embeddings"]
				)
				losses["thought_alignment"] = thought_loss
			except Exception as e:
				# Log warning but continue training
				losses["thought_alignment"] = torch.tensor(0.0, device=device)

		# 3. Output alignment loss using teacher logits
		if "teacher_logits" in teacher_data:
			try:
				output_loss = self._compute_output_alignment_loss(
					student_logits, teacher_data["teacher_logits"]
				)
				losses["output_alignment"] = output_loss
			except Exception as e:
				losses["output_alignment"] = torch.tensor(0.0, device=device)

		# 4. Confidence matching loss
		if "confidence_scores" in student_outputs and "teacher_confidence" in teacher_data:
			try:
				confidence_loss = self._compute_confidence_loss(
					student_outputs["confidence_scores"],
					teacher_data["teacher_confidence"]
				)
				losses["confidence_matching"] = confidence_loss
			except Exception as e:
				losses["confidence_matching"] = torch.tensor(0.0, device=device)

		# Compute weighted total loss
		total_loss = self.alpha * generation_loss

		if "thought_alignment" in losses:
			total_loss += self.beta * losses["thought_alignment"]

		if "output_alignment" in losses:
			total_loss += self.gamma * losses["output_alignment"]

		if "confidence_matching" in losses:
			total_loss += 0.1 * losses["confidence_matching"]

		losses["total"] = total_loss

		return losses

	def _compute_thought_alignment_loss(self, student_hidden: torch.Tensor,
									  teacher_thoughts: torch.Tensor) -> torch.Tensor:
		"""Align student hidden states with teacher thought representations"""
		device = student_hidden.device
		teacher_thoughts = teacher_thoughts.to(device)

		# Handle dimension mismatch
		if student_hidden.size(-1) != teacher_thoughts.size(-1):
			self._init_thought_projector(student_hidden.size(-1), teacher_thoughts.size(-1))
			student_hidden = self.thought_projector(student_hidden)

		# Ensure sequence length compatibility
		min_seq_len = min(student_hidden.size(1), teacher_thoughts.size(1))
		student_trimmed = student_hidden[:, :min_seq_len, :]
		teacher_trimmed = teacher_thoughts[:, :min_seq_len, :]

		# Compute cosine similarity loss
		student_norm = F.normalize(student_trimmed, dim=-1)
		teacher_norm = F.normalize(teacher_trimmed, dim=-1)

		similarity = torch.sum(student_norm * teacher_norm, dim=-1)
		loss = 1.0 - similarity.mean()

		return torch.clamp(loss, 0.0, 2.0)  # Clamp for stability

	def _compute_output_alignment_loss(self, student_logits: torch.Tensor,
									 teacher_logits: torch.Tensor) -> torch.Tensor:
		"""Align student output distribution with teacher"""
		device = student_logits.device
		teacher_logits = teacher_logits.to(device)

		# Ensure vocabulary size compatibility
		if student_logits.size(-1) != teacher_logits.size(-1):
			min_vocab = min(student_logits.size(-1), teacher_logits.size(-1))
			student_logits = student_logits[..., :min_vocab]
			teacher_logits = teacher_logits[..., :min_vocab]

		# Ensure sequence length compatibility
		min_seq_len = min(student_logits.size(1), teacher_logits.size(1))
		student_logits = student_logits[:, :min_seq_len, :]
		teacher_logits = teacher_logits[:, :min_seq_len, :]

		# Apply temperature scaling
		student_soft = F.log_softmax(student_logits / self.temperature, dim=-1)
		teacher_soft = F.softmax(teacher_logits / self.temperature, dim=-1)

		# KL divergence loss
		kl_loss = F.kl_div(student_soft, teacher_soft, reduction='batchmean')

		return kl_loss * (self.temperature ** 2)

	def _compute_confidence_loss(self, student_confidence: torch.Tensor, teacher_confidence: torch.Tensor) -> torch.Tensor:
		"""Match confidence distributions between student and teacher"""
		device = student_confidence.device
		teacher_confidence = teacher_confidence.to(device)

		# Ensure shape compatibility
		if student_confidence.shape != teacher_confidence.shape:
			min_batch = min(student_confidence.size(0), teacher_confidence.size(0))
			min_seq = min(student_confidence.size(1), teacher_confidence.size(1))

			student_confidence = student_confidence[:min_batch, :min_seq]
			teacher_confidence = teacher_confidence[:min_batch, :min_seq]

		return F.mse_loss(student_confidence, teacher_confidence)

# ============================================================================
# FUNDAMENTAL COMPONENT 5: TRAINING INTEGRATION
# ============================================================================

@dataclass
class ParentChildTrainingConfig:
	"""Configuration for parent-child training setup"""
	# Parent model config
	parent_config: ParentModelConfig = field(default_factory=ParentModelConfig)

	# Data generation config
	data_generation_config: DataGenerationConfig = field(default_factory=DataGenerationConfig)

	# Child model training config (extends existing TrainingConfig)
	child_training_config: TrainingConfig = field(default_factory=TrainingConfig)

	# Knowledge distillation parameters
	distillation_alpha: float = 0.5  # Generation loss weight
	distillation_beta: float = 0.3   # Thought alignment weight
	distillation_gamma: float = 0.2  # Output alignment weight
	distillation_temperature: float = 3.0

	# Training phases
	use_curriculum: bool = True
	warmup_steps: int = 1000  # Steps with only generation loss
	distillation_start_step: int = 2000  # When to start distillation

	# Data refresh
	regenerate_data_interval: int = 10000  # Regenerate data every N steps
	keep_old_data_ratio: float = 0.3  # Fraction of old data to keep

class ParentChildDataset(Dataset):
	"""
	Dataset that combines generated training data with parent model guidance.

	This dataset systematically processes examples generated by the parent model:
	1. Loads examples generated by the parent model
	2. Processes thoughts and outputs into consistent training format
	3. Provides both text and structured reasoning data
	4. Maintains compatibility with existing tokenization schemes
	"""

	def __init__(self, data_file: str, tokenizer, max_length: int = 1024):
		self.tokenizer = tokenizer
		self.max_length = max_length
		self.thought_processor = ThoughtProcessor()

		# Ensure pad token exists
		if self.tokenizer.pad_token is None:
			self.tokenizer.pad_token = self.tokenizer.eos_token

		# Load generated data
		self.examples = self._load_data(data_file)

		print(f"Loaded {len(self.examples)} parent-child training examples")

	def _load_data(self, data_file: str) -> List[Dict[str, Any]]:
		"""Load and preprocess generated training data with validation"""
		examples = []

		try:
			with open(data_file, 'r') as f:
				for line_num, line in enumerate(f, 1):
					try:
						data = json.loads(line.strip())

						# Validate required fields
						if not all(key in data for key in ['prompt', 'output']):
							continue

						# Process thoughts if available
						if data.get("thoughts"):
							thought_structure = self.thought_processor.extract_reasoning_structure(
								data["thoughts"]
							)
							data["thought_structure"] = thought_structure

						examples.append(data)

					except json.JSONDecodeError as e:
						print(f"Warning: Invalid JSON at line {line_num}: {e}")
						continue
					except Exception as e:
						print(f"Warning: Error processing line {line_num}: {e}")
						continue

		except FileNotFoundError:
			print(f"Warning: Data file {data_file} not found. Dataset will be empty.")
			return []

		return examples

	def __len__(self) -> int:
		return len(self.examples)

	def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
		"""Get formatted training example with proper tokenization"""
		example = self.examples[idx]

		# Create input text combining prompt and thoughts
		prompt = example["prompt"]
		thoughts = example.get("thoughts", "")
		output = example.get("output", "")

		# Create structured format that encourages internal reasoning
		if thoughts:
			# Format: Human prompt -> [Internal thoughts] -> Assistant response
			full_text = f"Human: {prompt}\n\nAssistant: {output}"
			# Store thoughts separately for potential distillation use
			thought_text = thoughts
		else:
			full_text = f"Human: {prompt}\n\nAssistant: {output}"
			thought_text = ""

		# Tokenize main text
		try:
			encoding = self.tokenizer(
				full_text,
				max_length=self.max_length,
				padding="max_length",
				truncation=True,
				return_tensors="pt"
			)

			input_ids = encoding["input_ids"].squeeze(0)
			attention_mask = encoding["attention_mask"].squeeze(0)

			# Create labels (compute loss only on assistant response)
			labels = input_ids.clone()

			# Find assistant response start and mask previous tokens
			assistant_start = self._find_assistant_start(input_ids)
			if assistant_start is not None:
				labels[:assistant_start] = -100

		except Exception as e:
			# Fallback to simple encoding if complex tokenization fails
			print(f"Warning: Tokenization failed for example {idx}: {e}")
			simple_text = f"{prompt} {output}"
			encoding = self.tokenizer(
				simple_text,
				max_length=self.max_length,
				padding="max_length",
				truncation=True,
				return_tensors="pt"
			)
			input_ids = encoding["input_ids"].squeeze(0)
			attention_mask = encoding["attention_mask"].squeeze(0)
			labels = input_ids.clone()

		result = {
			"input_ids": input_ids,
			"attention_mask": attention_mask,
			"labels": labels,
			"task_type": example.get("task_type", "general"),
		}

		# Add thought-related data for distillation
		if thought_text:
			try:
				# Tokenize thoughts for potential embedding generation
				thought_encoding = self.tokenizer(
					thought_text,
					max_length=min(512, self.max_length),
					padding="max_length",
					truncation=True,
					return_tensors="pt"
				)
				result["thought_input_ids"] = thought_encoding["input_ids"].squeeze(0)
				result["thought_attention_mask"] = thought_encoding["attention_mask"].squeeze(0)
			except:
				# If thought tokenization fails, provide empty placeholders
				result["thought_input_ids"] = torch.zeros(512, dtype=torch.long)
				result["thought_attention_mask"] = torch.zeros(512, dtype=torch.long)

		# Add thought structure if available
		if "thought_structure" in example:
			structure = example["thought_structure"]
			result["complexity_score"] = torch.tensor(structure["complexity_score"], dtype=torch.float32)
			result["reasoning_pattern_count"] = torch.tensor(
				sum(structure["reasoning_patterns"].values()), dtype=torch.float32
			)
		else:
			result["complexity_score"] = torch.tensor(0.0, dtype=torch.float32)
			result["reasoning_pattern_count"] = torch.tensor(0.0, dtype=torch.float32)

		return result

	def _find_assistant_start(self, input_ids: torch.Tensor) -> Optional[int]:
		"""Find the start position of assistant response for label masking"""
		try:
			# Try to find "Assistant:" token sequence
			assistant_text = "Assistant:"
			assistant_tokens = self.tokenizer.encode(assistant_text, add_special_tokens=False)

			# Search for the token sequence
			for i in range(len(input_ids) - len(assistant_tokens)):
				if torch.equal(input_ids[i:i+len(assistant_tokens)], torch.tensor(assistant_tokens)):
					return i + len(assistant_tokens)

			# Fallback: find any reasonable split point
			text = self.tokenizer.decode(input_ids, skip_special_tokens=True)
			if "Assistant:" in text:
				# Re-encode to find position
				parts = text.split("Assistant:", 1)
				if len(parts) == 2:
					prefix = parts[0] + "Assistant:"
					prefix_tokens = self.tokenizer.encode(prefix, add_special_tokens=False)
					return len(prefix_tokens)

		except Exception:
			pass

		return None

class ParentChildTrainer(DiffusionTrainer):
	"""
	Enhanced trainer that incorporates parent model guidance.

	This trainer systematically extends the base DiffusionTrainer with:
	1. Knowledge distillation capabilities with proper loss integration
	2. Synchronized data generation from parent model
	3. Thought-guided training objectives with progressive complexity
	4. Curriculum learning with parent guidance integration
	"""

	def __init__(self, config: ParentChildTrainingConfig):
		# Store parent-child specific config
		self.parent_child_config = config

		# Initialize base trainer first with child config
		super().__init__(config.child_training_config)

		# Initialize parent-specific components after base initialization
		self.data_generator = DataGenerator(
			config.parent_config,
			config.data_generation_config
		)

		# Override loss function with knowledge distillation
		self.loss_fn = KnowledgeDistillationLoss(
			alpha=config.distillation_alpha,
			beta=config.distillation_beta,
			gamma=config.distillation_gamma,
			temperature=config.distillation_temperature,
			hidden_dim=config.child_training_config.model_config.hidden_dim
		)

		# Data refresh tracking
		self.last_data_generation = 0
		self.data_initialized = False

		# Training phase tracking
		self.distillation_active = False

	def _initialize_training_data_sync(self) -> None:
		"""Synchronous wrapper for data initialization to work with base trainer"""
		if self.data_initialized:
			return

		self.logger.info("Generating initial training data from parent model...")

		# Run async data generation in sync context
		try:
			loop = asyncio.get_event_loop()
		except RuntimeError:
			loop = asyncio.new_event_loop()
			asyncio.set_event_loop(loop)

		try:
			training_data = loop.run_until_complete(
				self.data_generator.generate_training_data()
			)
		except Exception as e:
			self.logger.error(f"Failed to generate initial training data: {e}")
			# Create minimal fallback dataset
			self._create_fallback_dataset()
			return

		# Create training dataset with generated data
		data_file = Path(self.parent_child_config.data_generation_config.output_dir) / "training_data.jsonl"

		if data_file.exists():
			# Replace existing dataset
			self.train_dataset = ParentChildDataset(
				str(data_file),
				self.train_dataset.tokenizer,
				self.config.max_length
			)

			# Recreate data loader with new dataset
			self._recreate_data_loader()

			self.logger.info(f"Training data initialized with {len(self.train_dataset)} examples")
		else:
			self.logger.warning("Generated data file not found, using fallback dataset")
			self._create_fallback_dataset()

		self.data_initialized = True

	def _create_fallback_dataset(self) -> None:
		"""Create a minimal fallback dataset if data generation fails"""
		fallback_data = [
			{
				"id": "fallback_1",
				"prompt": "What is artificial intelligence?",
				"thoughts": "I need to explain AI in simple terms, covering its definition and basic applications.",
				"output": "Artificial intelligence (AI) refers to computer systems that can perform tasks typically requiring human intelligence, such as learning, reasoning, and problem-solving.",
				"task_type": "factual"
			},
			{
				"id": "fallback_2",
				"prompt": "How do you solve a complex problem?",
				"thoughts": "I should break this down into steps: understand the problem, gather information, consider options, and implement solutions.",
				"output": "To solve complex problems: 1) Clearly define the problem, 2) Gather relevant information, 3) Break it into smaller parts, 4) Consider multiple solutions, 5) Test and refine your approach.",
				"task_type": "reasoning"
			}
		]

		# Save fallback data
		fallback_file = Path(self.parent_child_config.data_generation_config.output_dir) / "fallback_data.jsonl"
		fallback_file.parent.mkdir(parents=True, exist_ok=True)

		with open(fallback_file, 'w') as f:
			for example in fallback_data:
				f.write(json.dumps(example) + '\n')

		# Create dataset from fallback
		self.train_dataset = ParentChildDataset(
			str(fallback_file),
			self.train_dataset.tokenizer,
			self.config.max_length
		)

		self._recreate_data_loader()
		self.logger.info(f"Created fallback dataset with {len(self.train_dataset)} examples")

	def _recreate_data_loader(self) -> None:
		"""Recreate data loader with new dataset"""
		# Handle distributed training
		train_sampler = None
		if self.config.local_rank != -1:
			try:
				from torch.utils.data.distributed import DistributedSampler
				train_sampler = DistributedSampler(
					self.train_dataset,
					num_replicas=self.config.world_size,
					rank=self.config.local_rank,
					shuffle=True
				)
			except ImportError:
				self.logger.warning("DistributedSampler not available, using standard sampling")

		self.train_loader = DataLoader(
			self.train_dataset,
			batch_size=self.config.batch_size,
			sampler=train_sampler,
			shuffle=(train_sampler is None),
			num_workers=self.config.dataloader_num_workers,
			pin_memory=True,
			drop_last=True
		)

	def train_step(self, batch: Dict[str, torch.Tensor]) -> Dict[str, float]:
		"""Enhanced training step with knowledge distillation integration"""
		self.model.train()

		# Validate inputs
		if not validate_inputs(batch):
			return {"total": 0.0, "error": 1.0}

		# Move batch to device
		batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}

		# Determine training phase based on step progression
		use_distillation = (
			self.global_step >= self.parent_child_config.distillation_start_step and
			self.global_step >= self.parent_child_config.warmup_steps
		)

		# Update distillation status
		if use_distillation and not self.distillation_active:
			self.distillation_active = True
			self.logger.info(f"Knowledge distillation activated at step {self.global_step}")

		# Get diffusion steps from curriculum
		diffusion_steps = self.curriculum.get_diffusion_steps(self.global_step, self.config.max_steps)

		# Mixed precision context
		dtype = torch.bfloat16 if self.config.bf16 else torch.float16
		precision_context = torch.cuda.amp.autocast(
			enabled=self.config.fp16 or self.config.bf16,
			dtype=dtype
		)

		try:
			with precision_context:
				# Forward pass through student model
				outputs = self.model(
					input_ids=batch["input_ids"],
					attention_mask=batch["attention_mask"],
					max_steps=diffusion_steps,
					use_diffusion=True,
					return_all_steps=True
				)

				# Prepare teacher data for distillation
				teacher_data = {}
				if use_distillation:
					teacher_data = self._prepare_teacher_data(batch, outputs)

				# Compute losses based on training phase
				if use_distillation and teacher_data:
					losses = self.loss_fn(outputs, teacher_data, batch)
				else:
					# Use standard diffusion loss during warmup
					step_outputs = outputs.get("all_states", [outputs])
					standard_loss_fn = DiffusionLoss(self.config)
					losses = standard_loss_fn(outputs, batch, step_outputs, diffusion_steps)

			# Backward pass with gradient scaling
			scaled_loss = self.scaler.scale(losses["total"])
			scaled_loss.backward()

			# Update training metrics
			self._update_training_metrics(batch, outputs)

		except Exception as e:
			self.logger.error(f"Training step failed: {e}")
			return {"total": float('inf'), "error": 1.0}

		# Gradient accumulation and optimization
		if (self.global_step + 1) % self.config.gradient_accumulation_steps == 0:
			# Gradient clipping
			self.scaler.unscale_(self.optimizer)
			grad_norm = torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.max_grad_norm)

			# Optimizer step
			self.scaler.step(self.optimizer)
			self.scaler.update()

			# Learning rate scheduling
			if self.global_step < self.config.warmup_steps:
				self.warmup_scheduler.step()
			else:
				self.scheduler.step()

			self.optimizer.zero_grad()

			losses["grad_norm"] = grad_norm.item() if isinstance(grad_norm, torch.Tensor) else grad_norm

		# Add training phase information
		losses["distillation_active"] = float(use_distillation)
		losses["diffusion_steps"] = diffusion_steps

		return {k: v.item() if torch.is_tensor(v) else v for k, v in losses.items()}

	def _prepare_teacher_data(self, batch: Dict[str, torch.Tensor],
							student_outputs: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
		"""Prepare teacher data for knowledge distillation with proper handling"""
		teacher_data = {}

		# Generate teacher confidence from complexity score
		if "complexity_score" in batch:
			complexity = batch["complexity_score"]

			# Convert complexity to confidence (normalized sigmoid)
			teacher_confidence = torch.sigmoid(complexity * 0.5)  # Scale for reasonable range

			# Expand to match sequence length
			seq_len = student_outputs["logits"].size(1)
			if teacher_confidence.dim() == 1:
				teacher_confidence = teacher_confidence.unsqueeze(1)

			# Broadcast to sequence length
			teacher_confidence = teacher_confidence.expand(-1, seq_len)
			teacher_data["teacher_confidence"] = teacher_confidence

		# Generate thought embeddings if thought tokens are available
		if "thought_input_ids" in batch and torch.sum(batch["thought_input_ids"]) > 0:
			try:
				# Use the student model to encode thoughts
				with torch.no_grad():
					thought_outputs = self.model.transformer(
						input_ids=batch["thought_input_ids"],
						attention_mask=batch["thought_attention_mask"],
						use_cache=False
					)

					# Use mean pooling of hidden states as thought embeddings
					thought_embeddings = thought_outputs["hidden_states"].mean(dim=1, keepdim=True)

					# Expand to match student sequence length
					seq_len = student_outputs["hidden_states"].size(1)
					thought_embeddings = thought_embeddings.expand(-1, seq_len, -1)

					teacher_data["thought_embeddings"] = thought_embeddings

			except Exception as e:
				self.logger.debug(f"Thought embedding generation failed: {e}")

		return teacher_data

	def _refresh_training_data_sync(self) -> None:
		"""Synchronous wrapper for data refresh"""
		if (self.global_step - self.last_data_generation <
			self.parent_child_config.regenerate_data_interval):
			return

		self.logger.info("Refreshing training data with new examples from parent model...")

		# Run async data generation
		try:
			loop = asyncio.get_event_loop()
		except RuntimeError:
			loop = asyncio.new_event_loop()
			asyncio.set_event_loop(loop)

		try:
			new_data = loop.run_until_complete(
				self.data_generator.generate_training_data()
			)

			# Update dataset
			data_file = Path(self.parent_child_config.data_generation_config.output_dir) / "training_data.jsonl"
			if data_file.exists():
				# Create new dataset and loader
				self.train_dataset = ParentChildDataset(
					str(data_file),
					self.train_dataset.tokenizer,
					self.config.max_length
				)
				self._recreate_data_loader()

				self.last_data_generation = self.global_step
				self.logger.info("Training data refreshed successfully")

		except Exception as e:
			self.logger.warning(f"Data refresh failed: {e}")

	def train(self) -> None:
		"""Enhanced training loop with proper parent-child integration"""
		# Initialize training data from parent model
		self._initialize_training_data_sync()

		# Start standard training loop
		self.logger.info("Starting Parent-Child Diffusion LLM training...")

		# Create output directory
		Path(self.config.output_dir).mkdir(parents=True, exist_ok=True)

		data_iter = iter(self.train_loader)
		moving_avg_loss = None
		best_checkpoint_step = 0

		for step in range(self.global_step, self.config.max_steps):
			self.global_step = step

			try:
				batch = next(data_iter)
			except StopIteration:
				data_iter = iter(self.train_loader)
				batch = next(data_iter)

			# Training step
			start_time = time.time()
			losses = self.train_step(batch)
			step_time = time.time() - start_time

			# Handle training errors
			if losses.get("error", 0) > 0:
				self.logger.warning(f"Training error at step {step}")
				continue

			# Moving average loss
			if moving_avg_loss is None:
				moving_avg_loss = losses["total"]
			else:
				moving_avg_loss = 0.9 * moving_avg_loss + 0.1 * losses["total"]

			# Periodic data refresh (less frequent to avoid overhead)
			if step % 5000 == 0 and step > 0:  # Every 5000 steps
				self._refresh_training_data_sync()
				data_iter = iter(self.train_loader)  # Recreate iterator

			# Comprehensive logging
			if step % self.config.logging_steps == 0:
				lr = self.optimizer.param_groups[0]['lr']

				log_dict = {
					"step": step,
					"learning_rate": lr,
					"moving_avg_loss": moving_avg_loss,
					"step_time": step_time,
					"dataset_size": len(self.train_dataset),
					**losses
				}

				self.logger.info(
					f"Step {step}: Loss = {moving_avg_loss:.4f}, "
					f"LR = {lr:.2e}, Distillation = {losses.get('distillation_active', False)}, "
					f"Diffusion Steps = {losses.get('diffusion_steps', 0)}, "
					f"Time = {step_time:.2f}s"
				)

				if self.config.wandb_project and self.config.local_rank <= 0:
					wandb.log(log_dict, step=step)

			# Evaluation
			if step > 0 and step % self.config.eval_steps == 0:
				eval_start_time = time.time()
				val_losses = self.evaluate()
				eval_time = time.time() - eval_start_time

				if val_losses and self.config.local_rank <= 0:
					self.logger.info(f"Validation - Step {step}: {val_losses}")

					if self.config.wandb_project:
						wandb.log(val_losses, step=step)

					val_loss = val_losses.get("val_total", float('inf'))
					if val_loss < self.best_val_loss:
						self.best_val_loss = val_loss
						best_checkpoint_step = step
						save_checkpoint(
							self.model, self.optimizer, self.scheduler,
							step, self.config, val_losses, self.train_dataset.tokenizer
						)
						self.logger.info(f"New best validation loss: {val_loss:.4f}")

			# Regular checkpointing
			if step > 0 and step % self.config.save_steps == 0:
				save_checkpoint(
					self.model, self.optimizer, self.scheduler,
					step, self.config, losses, self.train_dataset.tokenizer
				)

		self.logger.info(f"Training completed! Best checkpoint at step {best_checkpoint_step}")

# ============================================================================
# MAIN TRAINING SCRIPT WITH PROPER ERROR HANDLING
# ============================================================================

def main():
	"""
	Main function to run parent-child training with comprehensive error handling.

	This function orchestrates the complete training process by:
	1. Setting up the training configuration systematically
	2. Initializing all necessary components with proper validation
	3. Running the training loop with robust error handling
	4. Providing clear feedback on training progress and issues
	"""

	# Configure comprehensive training setup with validated parameters
	config = ParentChildTrainingConfig(
		# Parent model configuration for DeepSeek-R1
		parent_config=ParentModelConfig(
			model_name="DeepSeek-R1-0528-Qwen3-8B-BF16:latest",
			ollama_host="http://localhost:11434",
			max_tokens=2048,
			temperature=0.7,
			concurrent_requests=4,
			max_retries=3,
			timeout=120
		),

		# Data generation configuration with balanced task distribution
		data_generation_config=DataGenerationConfig(
			num_examples_per_category=1000,  # Start smaller for testing
			max_concurrent_generations=4,
			output_dir="./generated_training_data",
			reasoning_weight=0.3,   # Emphasize reasoning tasks
			factual_weight=0.2,
			creative_weight=0.2,
			instruction_weight=0.2,
			conversation_weight=0.1,
			min_thought_length=50,
			min_output_length=20,
			max_output_length=1000
		),

		# Child model training configuration optimized for parent-child learning
		child_training_config=TrainingConfig(
			model_config=DiffusionConfig(
				vocab_size=50257,
				hidden_dim=768,
				num_layers=12,
				num_heads=12,
				max_length=1024,
				diffusion_steps=14,
				use_kv_cache=True,
				confidence_calibration=True
			),
			learning_rate=3e-5,
			batch_size=4,  # Smaller batch size for stability
			max_steps=25000,  # Reduced for initial testing
			warmup_steps=1000,
			gradient_accumulation_steps=4,
			eval_steps=1000,
			save_steps=2500,
			logging_steps=50,
			output_dir="./parent_child_checkpoints",
			tokenizer_name="gpt2",
			wandb_project="parent-child-diffusion-llm",
			run_name="deepseek-r1-distillation-v1",
			bf16=True,
			max_grad_norm=1.0
		),

		# Knowledge distillation parameters carefully tuned
		distillation_alpha=0.6,    # Primary focus on generation quality
		distillation_beta=0.25,    # Moderate thought alignment
		distillation_gamma=0.15,   # Light output alignment
		distillation_temperature=3.0,

		# Training curriculum with progressive complexity
		use_curriculum=True,
		warmup_steps=1000,
		distillation_start_step=2000,
		regenerate_data_interval=10000  # Refresh data periodically
	)

	# Setup comprehensive logging with multiple handlers
	logging.basicConfig(
		level=logging.INFO,
		format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
		handlers=[
			logging.FileHandler('parent_child_training.log'),
			logging.StreamHandler()
		]
	)

	logger = logging.getLogger(__name__)
	logger.info("Starting Parent-Child Diffusion LLM Training System")

	# Validate system requirements before starting
	try:
		_validate_system_requirements()
		logger.info("System requirements validated successfully")
	except Exception as e:
		logger.error(f"System validation failed: {e}")
		return

	# Test Ollama connection before proceeding
	try:
		_test_ollama_connection(config.parent_config)
		logger.info("Ollama connection validated successfully")
	except Exception as e:
		logger.error(f"Ollama connection failed: {e}")
		logger.error("Please ensure Ollama is running and the model is available")
		return

	try:
		# Initialize trainer with comprehensive error handling
		logger.info("Initializing Parent-Child Trainer...")
		trainer = ParentChildTrainer(config)

		# Start training with proper exception handling
		logger.info("Beginning training process...")
		trainer.train()

		logger.info("Training completed successfully!")

	except KeyboardInterrupt:
		logger.info("Training interrupted by user")
	except Exception as e:
		logger.error(f"Training failed with error: {str(e)}")
		logger.error("Check the log file for detailed error information")
		raise

def _validate_system_requirements() -> None:
	"""Validate that all required dependencies and system components are available"""
	required_packages = ['torch', 'transformers', 'aiohttp', 'wandb', 'numpy']

	for package in required_packages:
		try:
			__import__(package)
		except ImportError:
			raise ImportError(f"Required package '{package}' not found. Please install it.")

	# Check CUDA availability
	if torch.cuda.is_available():
		logger = logging.getLogger(__name__)
		logger.info(f"CUDA available with {torch.cuda.device_count()} devices")
	else:
		print("Warning: CUDA not available, training will use CPU (very slow)")

def _test_ollama_connection(config: ParentModelConfig) -> None:
	"""Test connection to Ollama server and model availability"""
	import requests

	try:
		# Test basic connectivity
		response = requests.get(f"{config.ollama_host}/api/tags", timeout=10)
		response.raise_for_status()

		# Check model availability
		models = response.json()
		model_names = [model["name"] for model in models.get("models", [])]

		if config.model_name not in model_names:
			available_models = ", ".join(model_names) if model_names else "none"
			raise ValueError(
				f"Model '{config.model_name}' not found. "
				f"Available models: {available_models}"
			)

	except requests.RequestException as e:
		raise ConnectionError(f"Cannot connect to Ollama at {config.ollama_host}: {e}")

if __name__ == "__main__":
	# Set multiprocessing start method for compatibility
	try:
		import multiprocessing
		multiprocessing.set_start_method('spawn', force=True)
	except:
		pass

	# Run the training
	main()
