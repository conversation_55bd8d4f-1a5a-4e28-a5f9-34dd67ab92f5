
from dataclasses import dataclass
from enum import Enum
from typing import Op<PERSON>, Tu<PERSON>, List, Dict, Any

import math
import torch
import torch.nn as nn
import torch.nn.functional as F
import warnings

@dataclass
class DiffusionConfig:
	"""Configuration for Diffusion LLM with validation"""
	vocab_size: int = 50257
	hidden_dim: int = 768
	num_layers: int = 12
	num_heads: int = 12
	max_length: int = 1024
	diffusion_steps: int = 14
	st_memory_size: int = 100
	mt_memory_size: int = 500
	lt_memory_size: int = 2000
	dropout: float = 0.1
	layer_norm_eps: float = 1e-5
	max_grad_norm: float = 1.0
	temperature_start: float = 1.5
	temperature_end: float = 0.1
	confidence_calibration: bool = True
	use_kv_cache: bool = True
	mixed_precision: bool = False

	def __post_init__(self):
		"""Validate configuration parameters"""
		assert self.hidden_dim % self.num_heads == 0, f"hidden_dim ({self.hidden_dim}) must be divisible by num_heads ({self.num_heads})"
		assert 0.0 <= self.dropout <= 1.0, f"dropout must be in [0, 1], got {self.dropout}"
		assert self.diffusion_steps > 0, f"diffusion_steps must be positive, got {self.diffusion_steps}"
		assert self.temperature_start > self.temperature_end > 0, "temperature_start must be > temperature_end > 0"

def validate_tensor_shape(tensor: torch.Tensor, expected_dims: int, name: str) -> None:
	"""Validate tensor shape and handle errors gracefully"""
	if tensor.dim() != expected_dims:
		raise ValueError(f"{name} expected {expected_dims}D tensor, got {tensor.dim()}D")
	if torch.isnan(tensor).any() or torch.isinf(tensor).any():
		warnings.warn(f"Found NaN or Inf values in {name}")
		tensor.clamp_(-1e6, 1e6)

def clip_gradients(model: nn.Module, max_norm: float) -> float:
	"""Clip gradients and return the gradient norm"""
	if max_norm <= 0:
		return 0.0
	return torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm).item()

# ============================================================================
# PHASE 1: FOUNDATION COMPONENTS WITH KV-CACHE
# ============================================================================

class KVCache:
	"""Key-Value cache for efficient transformer inference"""

	def __init__(self, batch_size: int, num_heads: int, max_length: int, head_dim: int, device: torch.device):
		self.batch_size = batch_size
		self.num_heads = num_heads
		self.max_length = max_length
		self.head_dim = head_dim
		self.device = device

		# Initialize cache tensors
		self.key_cache = torch.zeros(batch_size, num_heads, max_length, head_dim, device=device)
		self.value_cache = torch.zeros(batch_size, num_heads, max_length, head_dim, device=device)
		self.cache_length = 0

	def update(self, keys: torch.Tensor, values: torch.Tensor, start_pos: int) -> Tuple[torch.Tensor, torch.Tensor]:
		"""Update cache with new keys and values"""
		seq_len = keys.size(2)
		end_pos = start_pos + seq_len

		if end_pos > self.max_length:
			raise ValueError(f"Cache overflow: trying to store {end_pos} tokens in cache of size {self.max_length}")

		self.key_cache[:, :, start_pos:end_pos] = keys
		self.value_cache[:, :, start_pos:end_pos] = values
		self.cache_length = max(self.cache_length, end_pos)

		return self.key_cache[:, :, :end_pos], self.value_cache[:, :, :end_pos]

	def clear(self) -> None:
		"""Clear the cache"""
		self.key_cache.zero_()
		self.value_cache.zero_()
		self.cache_length = 0

class DiffusionScheduler(nn.Module):
	"""Enhanced scheduler with validation and configurability"""

	def __init__(self, config: DiffusionConfig):
		super().__init__()
		self.config = config
		self.register_buffer('step_schedule', self._create_schedule())

	def _create_schedule(self) -> torch.Tensor:
		"""Create edit count schedule with better interpolation"""
		steps = torch.arange(self.config.diffusion_steps, dtype=torch.float32)
		# Smooth exponential decay instead of linear
		schedule = torch.exp(-2.0 * steps / self.config.diffusion_steps)
		return torch.clamp(schedule, min=0.05, max=0.8)

	def get_edit_count(self, step: int, sequence_length: int) -> int:
		"""Calculate number of tokens to edit with bounds checking"""
		if step >= self.config.diffusion_steps:
			return 1

		ratio = self.step_schedule[step].item()
		edit_count = max(1, int(sequence_length * ratio))
		return min(edit_count, sequence_length // 2)  # Never edit more than half

	def get_temperature(self, step: int) -> float:
		"""Exponential temperature annealing with validation"""
		if step >= self.config.diffusion_steps:
			return self.config.temperature_end

		progress = step / self.config.diffusion_steps
		alpha = -math.log(self.config.temperature_end / self.config.temperature_start)
		temperature = self.config.temperature_start * math.exp(-alpha * progress)
		return max(temperature, self.config.temperature_end)

class AttentionSelector(nn.Module):
	"""Enhanced attention with KV-cache support and better error handling"""

	def __init__(self, config: DiffusionConfig):
		super().__init__()
		self.config = config
		self.hidden_dim = config.hidden_dim
		self.num_heads = config.num_heads
		self.head_dim = config.hidden_dim // config.num_heads

		self.importance_head = nn.Linear(config.hidden_dim, 1)
		self.query_proj = nn.Linear(config.hidden_dim, config.hidden_dim)
		self.key_proj = nn.Linear(config.hidden_dim, config.hidden_dim)
		self.value_proj = nn.Linear(config.hidden_dim, config.hidden_dim)
		self.out_proj = nn.Linear(config.hidden_dim, config.hidden_dim)

		self.dropout = nn.Dropout(config.dropout)
		self.kv_cache: Optional[KVCache] = None

		# Initialize weights properly
		self._init_weights()

	def _init_weights(self) -> None:
		"""Initialize weights with proper scaling"""
		for module in [self.query_proj, self.key_proj, self.value_proj, self.out_proj]:
			nn.init.xavier_uniform_(module.weight)
			if module.bias is not None:
				nn.init.zeros_(module.bias)

		nn.init.xavier_uniform_(self.importance_head.weight)
		nn.init.zeros_(self.importance_head.bias)

	def setup_kv_cache(self, batch_size: int, max_length: int, device: torch.device) -> None:
		"""Initialize KV cache for inference"""
		if self.config.use_kv_cache:
			self.kv_cache = KVCache(batch_size, self.num_heads, max_length, self.head_dim, device)

	def select_tokens_to_edit(self, hidden_states: torch.Tensor,
							confidence_scores: torch.Tensor,
							edit_count: int) -> torch.Tensor:
		"""Vectorized token selection with proper error handling"""
		validate_tensor_shape(hidden_states, 3, "hidden_states")
		validate_tensor_shape(confidence_scores, 2, "confidence_scores")

		batch_size, seq_len = confidence_scores.shape

		if edit_count >= seq_len:
			edit_count = seq_len - 1

		# Vectorized importance computation
		importance = self.importance_head(hidden_states).squeeze(-1)
		importance = torch.clamp(importance, -10, 10)  # Numerical stability

		# Combine confidence and importance (lower is worse)
		selection_scores = confidence_scores + 0.1 * torch.sigmoid(importance)

		# Vectorized top-k selection
		_, indices = torch.topk(selection_scores, edit_count, dim=-1, largest=False)
		return indices

	def compute_attention_weights(self, query: torch.Tensor, key: torch.Tensor,
							value: torch.Tensor, mask: Optional[torch.Tensor] = None,
							use_cache: bool = False, cache_position: int = 0) -> Tuple[torch.Tensor, torch.Tensor]:
		"""Optimized multi-head attention with KV-cache support"""
		validate_tensor_shape(query, 3, "query")
		validate_tensor_shape(key, 3, "key")
		validate_tensor_shape(value, 3, "value")

		batch_size, seq_len = query.shape[:2]
		key_len = key.shape[1]

		# Project and reshape
		Q = self.query_proj(query).view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
		K = self.key_proj(key).view(batch_size, key_len, self.num_heads, self.head_dim).transpose(1, 2)
		V = self.value_proj(value).view(batch_size, key_len, self.num_heads, self.head_dim).transpose(1, 2)

		# Use KV cache if available and requested
		if use_cache and self.kv_cache is not None:
			K, V = self.kv_cache.update(K, V, cache_position)
			key_len = K.shape[2]  # Update key_len after cache update

		# Scaled dot-product attention with numerical stability
		scale = 1.0 / math.sqrt(self.head_dim)
		scores = torch.matmul(Q, K.transpose(-2, -1)) * scale
		scores = torch.clamp(scores, -50, 50)

		# Validate mask shape
		if mask is not None:
			validate_tensor_shape(mask, 4, "attention_mask")
			expected_shape = (batch_size, self.num_heads, seq_len, key_len)
			if mask.shape != expected_shape:
				raise ValueError(f"Mask shape {mask.shape} does not match expected {expected_shape}")
			scores = scores.masked_fill(mask == 0, float('-inf'))

		attention_weights = F.softmax(scores, dim=-1)
		attention_weights = self.dropout(attention_weights)

		attended_values = torch.matmul(attention_weights, V)
		attended_values = attended_values.transpose(1, 2).contiguous().view(
			batch_size, seq_len, self.hidden_dim)
		output = self.out_proj(attended_values)

		return output, attention_weights

class BaseTransformer(nn.Module):
	"""Enhanced transformer with proper initialization and error handling"""

	def __init__(self, config: DiffusionConfig):
		super().__init__()
		self.config = config

		# Token and position embeddings
		self.token_embedding = nn.Embedding(config.vocab_size, config.hidden_dim)
		self.position_embedding = nn.Embedding(config.max_length, config.hidden_dim)

		# Transformer layers
		self.layers = nn.ModuleList([
			TransformerLayer(config) for _ in range(config.num_layers)
		])

		# Memory cross-attention layer
		self.memory_attention = AttentionSelector(config)
		self.memory_norm = nn.LayerNorm(config.hidden_dim, eps=config.layer_norm_eps)

		# Output head
		self.ln_f = nn.LayerNorm(config.hidden_dim, eps=config.layer_norm_eps)
		self.lm_head = nn.Linear(config.hidden_dim, config.vocab_size, bias=False)

		self.dropout = nn.Dropout(config.dropout)

		# Cache for attention masks
		self._attention_mask_cache: Dict[Tuple[int, int], torch.Tensor] = {}

		self._init_weights()

	def _init_weights(self) -> None:
		"""Proper weight initialization"""
		# Embedding initialization
		nn.init.normal_(self.token_embedding.weight, std=0.02)
		nn.init.normal_(self.position_embedding.weight, std=0.02)

		# LM head initialization (tied to token embedding)
		self.lm_head.weight = self.token_embedding.weight

	def _get_attention_mask(self, seq_len: int, device: torch.device, num_heads: int) -> torch.Tensor:
		"""Cached attention mask generation with proper shape for multi-head attention"""
		cache_key = (seq_len, device.index if device.index is not None else 0)

		if cache_key not in self._attention_mask_cache:
			mask = torch.tril(torch.ones(seq_len, seq_len, device=device))
			# Expand to [1, num_heads, seq_len, seq_len] for broadcasting
			mask = mask.unsqueeze(0).unsqueeze(1).expand(-1, num_heads, -1, -1)
			self._attention_mask_cache[cache_key] = mask

		return self._attention_mask_cache[cache_key]

	def forward(self, input_ids: torch.Tensor,
			memory_states: Optional[torch.Tensor] = None,
			attention_mask: Optional[torch.Tensor] = None,
			position_ids: Optional[torch.Tensor] = None,
			use_cache: bool = False) -> Dict[str, torch.Tensor]:
		"""Enhanced forward pass with proper error handling"""
		validate_tensor_shape(input_ids, 2, "input_ids")
		batch_size, seq_len = input_ids.shape
		device = input_ids.device

		# Validate sequence length
		if seq_len > self.config.max_length:
			raise ValueError(f"Sequence length {seq_len} exceeds maximum {self.config.max_length}")

		# Generate position IDs if not provided
		if position_ids is None:
			position_ids = torch.arange(seq_len, device=device).unsqueeze(0).expand(batch_size, -1)

		# Embeddings with gradient clipping
		with torch.autograd.set_detect_anomaly(True):
			token_emb = self.token_embedding(input_ids)
			pos_emb = self.position_embedding(position_ids)
			hidden_states = self.dropout(token_emb + pos_emb)
			hidden_states = torch.clamp(hidden_states, -10, 10)

		# Use provided attention mask or generate one
		if attention_mask is None:
			attention_mask = self._get_attention_mask(seq_len, device, self.config.num_heads)
		else:
			validate_tensor_shape(attention_mask, 2, "attention_mask")
			if attention_mask.shape != (batch_size, seq_len):
				raise ValueError(f"attention_mask shape {attention_mask.shape} does not match expected ({batch_size}, {seq_len})")
			# Expand for multi-head attention
			attention_mask = attention_mask.unsqueeze(1).unsqueeze(1).expand(-1, self.config.num_heads, seq_len, seq_len)

		# Setup KV cache for all layers if needed
		if use_cache:
			for layer in self.layers:
				layer.attention.setup_kv_cache(batch_size, self.config.max_length, device)

		attention_weights = []

		# Pass through transformer layers
		for i, layer in enumerate(self.layers):
			try:
				hidden_states, layer_attn = layer(hidden_states, attention_mask, use_cache, i)
				attention_weights.append(layer_attn)

				# Gradient clipping per layer
				if self.training:
					clip_gradients(layer, self.config.max_grad_norm)

			except Exception as e:
				raise RuntimeError(f"Error in transformer layer {i}: {str(e)}")

		# Cross-attention to memory if provided
		if memory_states is not None:
			validate_tensor_shape(memory_states, 3, "memory_states")
			memory_output, memory_attn = self.cross_attend_to_memory(hidden_states, memory_states)
			hidden_states = self.memory_norm(hidden_states + memory_output)
			attention_weights.append(memory_attn)

		# Final layer norm and output projection
		hidden_states = self.ln_f(hidden_states)
		logits = self.lm_head(hidden_states)
		logs = torch.clamp(logits, -100, 100)

		return {
			'hidden_states': hidden_states,
			'logits': logits,
			'attention_weights': attention_weights
		}

	def cross_attend_to_memory(self, hidden_states: torch.Tensor,
							 memory_states: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
		"""Cross-attention with error handling"""
		try:
			memory_output, memory_attn = self.memory_attention.compute_attention_weights(
				query=hidden_states,
				key=memory_states,
				value=memory_states
			)
			return memory_output, memory_attn
		except Exception as e:
			warnings.warn(f"Memory cross-attention failed: {str(e)}")
			return torch.zeros_like(hidden_states), torch.zeros(1, 1, 1, 1, device=hidden_states.device)

class TransformerLayer(nn.Module):
	"""Enhanced transformer layer with gradient clipping and stability"""

	def __init__(self, config: DiffusionConfig):
		super().__init__()
		self.config = config
		self.attention = AttentionSelector(config)
		self.feed_forward = nn.Sequential(
			nn.Linear(config.hidden_dim, 4 * config.hidden_dim),
			nn.GELU(),
			nn.Linear(4 * config.hidden_dim, config.hidden_dim),
			nn.Dropout(config.dropout)
		)
		self.ln1 = nn.LayerNorm(config.hidden_dim, eps=config.layer_norm_eps)
		self.ln2 = nn.LayerNorm(config.hidden_dim, eps=config.layer_norm_eps)
		self.dropout = nn.Dropout(config.dropout)

		self._init_weights()

	def _init_weights(self) -> None:
		"""Initialize feed-forward weights"""
		for module in self.feed_forward:
			if isinstance(module, nn.Linear):
				nn.init.xavier_uniform_(module.weight)
				if module.bias is not None:
					nn.init.zeros_(module.bias)

	def forward(self, hidden_states: torch.Tensor,
				attention_mask: Optional[torch.Tensor] = None,
				use_cache: bool = False,
				cache_position: int = 0) -> Tuple[torch.Tensor, torch.Tensor]:
		"""Enhanced forward with numerical stability"""

		# Self-attention with residual connection
		residual = hidden_states
		normed_states = self.ln1(hidden_states)

		attn_output, attn_weights = self.attention.compute_attention_weights(
			normed_states, normed_states, normed_states, attention_mask, use_cache, cache_position
		)

		hidden_states = residual + self.dropout(attn_output)
		hidden_states = torch.clamp(hidden_states, -10, 10)  # Numerical stability

		# Feed-forward with residual connection
		residual = hidden_states
		ff_output = self.feed_forward(self.ln2(hidden_states))
		hidden_states = residual + ff_output
		hidden_states = torch.clamp(hidden_states, -10, 10)  # Numerical stability

		return hidden_states, attn_weights

# ============================================================================
# VECTORIZED MEMORY SYSTEM
# ============================================================================

class MemoryManager(nn.Module):
	"""Memory-optimized vectorized memory management system"""

	def __init__(self, config: DiffusionConfig):
		super().__init__()
		self.config = config

		self.short_term = ShortTermMemory(config)
		self.medium_term = MediumTermMemory(config)
		self.long_term = LongTermMemory(config)

		# Vectorized consolidation networks
		self.st_to_mt_proj = nn.Linear(config.hidden_dim, config.hidden_dim)
		self.mt_to_lt_proj = nn.Linear(config.hidden_dim, config.hidden_dim)

		# Consolidation scheduling
		self.register_buffer('last_mt_consolidation', torch.tensor(0))
		self.register_buffer('last_lt_consolidation', torch.tensor(0))

	def clear_all_memories(self) -> None:
		"""Clear all memory buffers to free memory"""
		self.short_term.clear()
		self.medium_term.clear()
		self.long_term.clear()

	def update_memories(self, new_content: torch.Tensor,
					   new_hidden: torch.Tensor, step: int) -> None:
		"""Vectorized memory updates with adaptive scheduling"""
		validate_tensor_shape(new_content, 2, "new_content")
		validate_tensor_shape(new_hidden, 3, "new_hidden")

		# Always update short-term memory
		self.short_term.append_batch(new_content, new_hidden)

		# Adaptive consolidation based on memory pressure
		st_utilization = self.short_term.get_utilization()
		mt_utilization = self.medium_term.get_utilization()

		# Consolidate to medium-term when ST is getting full
		if st_utilization > 0.8 or (step - self.last_mt_consolidation) >= 3:
			st_summary = self.short_term.get_batch_summary()
			if st_summary is not None:
				mt_content = self.st_to_mt_proj(st_summary)
				self.medium_term.consolidate_batch(mt_content)
				self.last_mt_consolidation = step

		# Consolidate to long-term when MT is getting full
		if mt_utilization > 0.8 or (step - self.last_lt_consolidation) >= 7:
			mt_summary = self.medium_term.get_batch_summary()
			if mt_summary is not None:
				lt_content = self.mt_to_lt_proj(mt_summary)
				self.long_term.store_batch_patterns(lt_content)
				self.last_lt_consolidation = step

	def read_memory_context(self, query_hidden_states: torch.Tensor) -> torch.Tensor:
		"""Vectorized memory retrieval"""
		validate_tensor_shape(query_hidden_states, 3, "query_hidden_states")
		batch_size, seq_len, hidden_dim = query_hidden_states.shape

		# Vectorized query processing
		query_summary = query_hidden_states.mean(dim=1)  # [B, H]

		# Parallel memory retrieval
		contexts = []

		st_context = self.short_term.get_batch_context(query_summary)
		if st_context is not None:
			contexts.append(st_context)

		mt_context = self.medium_term.retrieve_batch_relevant(query_summary)
		if mt_context is not None:
			contexts.append(mt_context)

		lt_context = self.long_term.query_batch_knowledge(query_summary)
		if lt_context is not None:
			contexts.append(lt_context)

		if contexts:
			# Vectorized context combination
			combined_memory = torch.cat(contexts, dim=1)
			return combined_memory
		else:
			return torch.zeros(batch_size, 1, hidden_dim, device=query_hidden_states.device)

class ShortTermMemory(nn.Module):
	"""Vectorized short-term memory with batch operations"""

	def __init__(self, config: DiffusionConfig):
		super().__init__()
		self.capacity = config.st_memory_size
		self.hidden_dim = config.hidden_dim

		# Vectorized circular buffers
		self.register_buffer('token_buffer', torch.zeros(self.capacity, dtype=torch.long))
		self.register_buffer('hidden_buffer', torch.zeros(self.capacity, self.hidden_dim))
		self.register_buffer('timestamps', torch.zeros(self.capacity))
		self.register_buffer('position', torch.tensor(0))
		self.register_buffer('filled', torch.tensor(0))

	def append_batch(self, tokens: torch.Tensor, hidden_states: torch.Tensor) -> None:
		"""Vectorized batch append operation"""
		batch_size, seq_len = tokens.shape
		total_tokens = batch_size * seq_len

		if total_tokens == 0:
			return

		# Flatten inputs for vectorized processing
		flat_tokens = tokens.reshape(-1)
		flat_hidden = hidden_states.reshape(-1, self.hidden_dim)

		# Vectorized circular buffer update
		positions = torch.arange(total_tokens, device=tokens.device) + self.position
		positions = positions % self.capacity

		# Batch update using advanced indexing
		self.token_buffer[positions] = flat_tokens
		self.hidden_buffer[positions] = flat_hidden
		self.timestamps[positions] = torch.full((total_tokens,), float(torch.randint(0, 1000000, (1,)).item()))

		# Update pointers
		self.position = (self.position + total_tokens) % self.capacity
		self.filled = min(self.filled + total_tokens, self.capacity)

	def get_batch_context(self, query_batch: torch.Tensor, window_size: int = 50) -> Optional[torch.Tensor]:
		"""Vectorized context retrieval"""
		if self.filled == 0:
			return None

		batch_size = query_batch.size(0)
		actual_window = min(window_size, self.filled.item())

		# Get most recent items using vectorized operations
		if self.filled < self.capacity:
			start_idx = max(0, self.position - actual_window)
			indices = torch.arange(start_idx, self.position, device=query_batch.device)
		else:
			start_pos = (self.position - actual_window) % self.capacity
			if start_pos < self.position:
				indices = torch.arange(start_pos, self.position, device=query_batch.device)
			else:
				indices = torch.cat([
					torch.arange(start_pos, self.capacity, device=query_batch.device),
					torch.arange(0, self.position, device=query_batch.device)
				])

		context = self.hidden_buffer[indices].unsqueeze(0).expand(batch_size, -1, -1)
		return context

	def get_batch_summary(self) -> Optional[torch.Tensor]:
		"""Vectorized summary computation"""
		if self.filled == 0:
			return None

		valid_states = self.hidden_buffer[:self.filled]
		weights = F.softmax(self.timestamps[:self.filled], dim=0)  # Recent bias
		summary = torch.sum(valid_states * weights.unsqueeze(-1), dim=0)
		return summary

	def get_utilization(self) -> float:
		"""Get memory utilization ratio"""
		return self.filled.item() / self.capacity

	def clear(self) -> None:
		"""Clear all memory buffers"""
		self.memory_buffer.zero_()
		self.timestamps.zero_()
		self.filled.zero_()
		self.position.zero_()

class MediumTermMemory(nn.Module):
	"""Vectorized medium-term memory with importance weighting"""

	def __init__(self, config: DiffusionConfig):
		super().__init__()
		self.capacity = config.mt_memory_size
		self.hidden_dim = config.hidden_dim

		self.register_buffer('memory_buffer', torch.zeros(self.capacity, self.hidden_dim))
		self.register_buffer('importance_scores', torch.zeros(self.capacity))
		self.register_buffer('access_counts', torch.zeros(self.capacity))
		self.register_buffer('position', torch.tensor(0))
		self.register_buffer('filled', torch.tensor(0))

		# Vectorized importance scoring
		self.importance_net = nn.Sequential(
			nn.Linear(self.hidden_dim, self.hidden_dim // 2),
			nn.ReLU(),
			nn.BatchNorm1d(self.hidden_dim // 2),
			nn.Linear(self.hidden_dim // 2, 1),
			nn.Sigmoid()
		)

	def consolidate_batch(self, content_batch: torch.Tensor) -> None:
		"""Vectorized batch consolidation"""
		if content_batch.dim() == 1:
			content_batch = content_batch.unsqueeze(0)

		batch_size = content_batch.size(0)

		# Vectorized importance computation
		importance_scores = self.importance_net(content_batch).squeeze(-1)

		# Vectorized buffer update
		positions = torch.arange(batch_size, device=content_batch.device) + self.position
		positions = positions % self.capacity

		self.memory_buffer[positions] = content_batch
		self.importance_scores[positions] = importance_scores
		self.access_counts[positions] = 0  # Reset access counts

		self.position = (self.position + batch_size) % self.capacity
		self.filled = min(self.filled + batch_size, self.capacity)

	def retrieve_batch_relevant(self, query_batch: torch.Tensor, top_k: int = 10) -> Optional[torch.Tensor]:
		"""Vectorized batch retrieval with importance weighting"""
		if self.filled == 0:
			return None

		batch_size = query_batch.size(0)
		valid_memories = self.memory_buffer[:self.filled]

		# Vectorized similarity computation
		query_norm = F.normalize(query_batch, dim=-1)  # [B, H]
		memory_norm = F.normalize(valid_memories, dim=-1)  # [M, H]

		# Batch matrix multiplication for similarities
		similarities = torch.matmul(query_norm, memory_norm.T)  # [B, M]

		# Apply importance weighting
		importance_weights = self.importance_scores[:self.filled].unsqueeze(0)  # [1, M]
		weighted_scores = similarities * importance_weights

		# Vectorized top-k selection
		k = min(top_k, self.filled.item())
		_, top_indices = torch.topk(weighted_scores, k, dim=-1)  # [B, k]

		# Update access counts (vectorized)
		unique_indices = torch.unique(top_indices.flatten())
		self.access_counts[unique_indices] += 1

		# Gather relevant memories for each batch element
		relevant_memories = valid_memories[top_indices]  # [B, k, H]
		return relevant_memories

	def get_batch_summary(self) -> Optional[torch.Tensor]:
		"""Importance-weighted vectorized summary"""
		if self.filled == 0:
			return None

		valid_memories = self.memory_buffer[:self.filled]
		valid_importance = self.importance_scores[:self.filled]

		# Vectorized weighted average
		weights = F.softmax(valid_importance, dim=0)
		summary = torch.sum(valid_memories * weights.unsqueeze(-1), dim=0)
		return summary

	def get_utilization(self) -> float:
		"""Get memory utilization ratio"""
		return self.filled.item() / self.capacity

	def clear(self) -> None:
		"""Clear all memory buffers"""
		self.memory_buffer.zero_()
		self.importance_scores.zero_()
		self.access_counts.zero_()
		self.position.zero_()
		self.filled.zero_()

class LongTermMemory(nn.Module):
	"""Vectorized long-term knowledge storage"""

	def __init__(self, config: DiffusionConfig):
		super().__init__()
		self.capacity = config.lt_memory_size
		self.hidden_dim = config.hidden_dim

		self.register_buffer('knowledge_buffer', torch.zeros(self.capacity, self.hidden_dim))
		self.register_buffer('access_counts', torch.zeros(self.capacity))
		self.register_buffer('knowledge_ages', torch.zeros(self.capacity))
		self.register_buffer('position', torch.tensor(0))
		self.register_buffer('filled', torch.tensor(0))

		# Vectorized knowledge encoding
		self.knowledge_encoder = nn.Sequential(
			nn.Linear(self.hidden_dim, self.hidden_dim),
			nn.LayerNorm(self.hidden_dim),
			nn.ReLU(),
			nn.Dropout(0.1),
			nn.Linear(self.hidden_dim, self.hidden_dim)
		)

	def store_batch_patterns(self, content_batch: torch.Tensor) -> None:
		"""Vectorized knowledge pattern storage"""
		if content_batch.dim() == 1:
			content_batch = content_batch.unsqueeze(0)

		batch_size = content_batch.size(0)

		# Vectorized knowledge encoding
		encoded_knowledge = self.knowledge_encoder(content_batch)

		# Vectorized buffer update
		positions = torch.arange(batch_size, device=content_batch.device) + self.position
		positions = positions % self.capacity

		self.knowledge_buffer[positions] = encoded_knowledge
		self.access_counts[positions] = 0
		self.knowledge_ages[positions] = 0  # Reset age for new knowledge

		self.position = (self.position + batch_size) % self.capacity
		self.filled = min(self.filled + batch_size, self.capacity)

		# Age existing knowledge (vectorized)
		self.knowledge_ages += 1

	def query_batch_knowledge(self, context_batch: torch.Tensor, top_k: int = 5) -> Optional[torch.Tensor]:
		"""Vectorized knowledge retrieval with aging"""
		if self.filled == 0:
			return None

		batch_size = context_batch.size(0)
		valid_knowledge = self.knowledge_buffer[:self.filled]

		# Vectorized relevance computation
		context_norm = F.normalize(context_batch, dim=-1)  # [B, H]
		knowledge_norm = F.normalize(valid_knowledge, dim=-1)  # [K, H]

		relevance_scores = torch.matmul(context_norm, knowledge_norm.T)  # [B, K]

		# Apply aging penalty (vectorized)
		age_penalty = 1.0 / (1.0 + 0.1 * self.knowledge_ages[:self.filled].unsqueeze(0))
		aged_scores = relevance_scores * age_penalty

		# Vectorized top-k selection
		k = min(top_k, self.filled.item())
		_, top_indices = torch.topk(aged_scores, k, dim=-1)  # [B, k]

		# Update access counts (vectorized)
		unique_indices = torch.unique(top_indices.flatten())
		self.access_counts[unique_indices] += 1

		relevant_knowledge = valid_knowledge[top_indices]  # [B, k, H]
		return relevant_knowledge

	def clear(self) -> None:
		"""Clear all memory buffers"""
		self.knowledge_buffer.zero_()
		self.access_counts.zero_()
		self.knowledge_ages.zero_()
		self.position.zero_()
		self.filled.zero_()

# ============================================================================
# VECTORIZED TOKEN OPERATIONS
# ============================================================================

class TokenEditor(nn.Module):
	"""Fully vectorized token editing operations"""

	def __init__(self, config: DiffusionConfig):
		super().__init__()
		self.vocab_size = config.vocab_size
		self.hidden_dim = config.hidden_dim
		self.max_length = config.max_length

		# Enhanced token generation
		self.token_generator = nn.Sequential(
			nn.Linear(self.hidden_dim, self.hidden_dim),
			nn.LayerNorm(self.hidden_dim),
			nn.ReLU(),
			nn.Dropout(0.1),
			nn.Linear(self.hidden_dim, self.vocab_size)
		)

		# Position embedding for edited tokens
		self.edit_position_embedding = nn.Embedding(self.max_length, self.hidden_dim)

	def insert_tokens_vectorized(self, sequence: torch.Tensor, positions: torch.Tensor,
								new_tokens: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
		"""Fully vectorized token insertion"""
		validate_tensor_shape(sequence, 2, "sequence")
		validate_tensor_shape(positions, 2, "positions")
		validate_tensor_shape(new_tokens, 2, "new_tokens")

		batch_size, seq_len = sequence.shape
		num_inserts = positions.size(1)

		# Create new sequence with space for insertions
		new_seq_len = min(seq_len + num_inserts, self.max_length)
		new_sequence = torch.zeros(batch_size, new_seq_len, dtype=sequence.dtype, device=sequence.device)

		# Vectorized position mapping
		batch_indices = torch.arange(batch_size, device=sequence.device).unsqueeze(1)

		# Sort positions for consistent insertion order
		sorted_positions, sort_indices = torch.sort(positions, dim=1)
		sorted_new_tokens = torch.gather(new_tokens, 1, sort_indices)

		# Vectorized insertion using scatter operations
		for b in range(batch_size):
			src_pos = 0
			dst_pos = 0
			for i in range(num_inserts):
				insert_pos = sorted_positions[b, i].item()

				# Copy tokens before insertion point
				copy_len = min(insert_pos - src_pos, new_seq_len - dst_pos)
				if copy_len > 0:
					new_sequence[b, dst_pos:dst_pos + copy_len] = sequence[b, src_pos:src_pos + copy_len]
					src_pos += copy_len
					dst_pos += copy_len

				# Insert new token
				if dst_pos < new_seq_len:
					new_sequence[b, dst_pos] = sorted_new_tokens[b, i]
					dst_pos += 1

			# Copy remaining tokens
			remaining_len = min(seq_len - src_pos, new_seq_len - dst_pos)
			if remaining_len > 0:
				new_sequence[b, dst_pos:dst_pos + remaining_len] = sequence[b, src_pos:src_pos + remaining_len]

		# Generate position mapping
		position_map = torch.arange(new_seq_len, device=sequence.device).unsqueeze(0).expand(batch_size, -1)

		return new_sequence, position_map

	def remove_tokens_vectorized(self, sequence: torch.Tensor, positions: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
		"""Fully vectorized token removal"""
		validate_tensor_shape(sequence, 2, "sequence")
		validate_tensor_shape(positions, 2, "positions")

		batch_size, seq_len = sequence.shape
		num_removes = positions.size(1)

		# Create removal mask (vectorized)
		remove_mask = torch.zeros_like(sequence, dtype=torch.bool)
		batch_indices = torch.arange(batch_size, device=sequence.device).unsqueeze(1)

		# Clamp positions to valid range
		valid_positions = torch.clamp(positions, 0, seq_len - 1)
		remove_mask[batch_indices, valid_positions] = True

		# Vectorized removal using boolean indexing
		new_sequence = torch.zeros_like(sequence)
		position_map = torch.zeros_like(sequence, dtype=torch.long)

		for b in range(batch_size):
			kept_tokens = sequence[b][~remove_mask[b]]
			new_len = len(kept_tokens)

			if new_len > 0:
				new_sequence[b, :new_len] = kept_tokens
				position_map[b, :new_len] = torch.arange(new_len, device=sequence.device)

			# Pad with zeros if needed
			if new_len < seq_len:
				new_sequence[b, new_len:] = 0  # Assume 0 is pad token

		return new_sequence, position_map

	def replace_tokens_vectorized(self, sequence: torch.Tensor, positions: torch.Tensor,
								 new_tokens: torch.Tensor) -> torch.Tensor:
		"""Fully vectorized token replacement"""
		validate_tensor_shape(sequence, 2, "sequence")
		validate_tensor_shape(positions, 2, "positions")
		validate_tensor_shape(new_tokens, 2, "new_tokens")

		batch_size, seq_len = sequence.shape
		new_sequence = sequence.clone()

		# Vectorized replacement using advanced indexing
		batch_indices = torch.arange(batch_size, device=sequence.device).unsqueeze(1)

		# Clamp positions to valid range
		valid_positions = torch.clamp(positions, 0, seq_len - 1)

		# Vectorized scatter operation
		new_sequence[batch_indices, valid_positions] = new_tokens

		return new_sequence

	def generate_replacement_tokens_batch(self, hidden_states: torch.Tensor,
										positions: torch.Tensor,
										temperature: float = 1.0) -> torch.Tensor:
		"""Vectorized batch token generation"""
		validate_tensor_shape(hidden_states, 3, "hidden_states")
		validate_tensor_shape(positions, 2, "positions")

		batch_size, seq_len, hidden_dim = hidden_states.shape
		num_positions = positions.size(1)

		# Vectorized position gathering
		batch_indices = torch.arange(batch_size, device=hidden_states.device).unsqueeze(1)
		valid_positions = torch.clamp(positions, 0, seq_len - 1)

		# Gather hidden states at specified positions (vectorized)
		pos_hidden = hidden_states[batch_indices, valid_positions]  # [B, P, H]

		# Add positional information
		pos_embeddings = self.edit_position_embedding(valid_positions)
		enhanced_hidden = pos_hidden + pos_embeddings

		# Vectorized token generation
		logits = self.token_generator(enhanced_hidden) / max(temperature, 1e-6)

		# Vectorized sampling
		flat_logits = logits.view(-1, self.vocab_size)
		flat_probs = F.softmax(flat_logits, dim=-1)
		flat_samples = torch.multinomial(flat_probs, num_samples=1)

		new_tokens = flat_samples.view(batch_size, num_positions)

		return new_tokens

# ============================================================================
# ENHANCED CONFIDENCE ESTIMATION
# ============================================================================

class ConfidenceEstimator(nn.Module):
	"""Enhanced confidence estimation with calibration"""

	def __init__(self, config: DiffusionConfig):
		super().__init__()
		self.config = config
		self.hidden_dim = config.hidden_dim

		# Multi-method confidence prediction
		self.confidence_head = nn.Sequential(
			nn.Linear(self.hidden_dim, self.hidden_dim // 2),
			nn.ReLU(),
			nn.BatchNorm1d(self.hidden_dim // 2),
			nn.Dropout(config.dropout),
			nn.Linear(self.hidden_dim // 2, 1),
			nn.Sigmoid()
		)

		# Calibration network
		if config.confidence_calibration:
			self.calibration_net = nn.Sequential(
				nn.Linear(3, 16),  # 3 confidence sources
				nn.ReLU(),
				nn.Linear(16, 1),
				nn.Sigmoid()
			)
		else:
			self.calibration_net = None

		# Monte Carlo dropout
		self.mc_dropout = nn.Dropout(0.15)

		# Temperature scaling for calibration
		self.register_parameter('temperature', nn.Parameter(torch.ones(1)))

	def estimate_token_confidence_batch(self, hidden_states: torch.Tensor,
									  logits: torch.Tensor,
									  mc_samples: int = 5) -> torch.Tensor:
		"""Vectorized batch confidence estimation with multiple methods"""
		validate_tensor_shape(hidden_states, 3, "hidden_states")
		validate_tensor_shape(logits, 3, "logits")

		batch_size, seq_len, hidden_dim = hidden_states.shape

		# Method 1: Direct confidence prediction (vectorized)
		flat_hidden = hidden_states.view(-1, hidden_dim)
		flat_confidence = self.confidence_head(flat_hidden)
		direct_confidence = flat_confidence.view(batch_size, seq_len)

		# Method 2: Entropy-based uncertainty (vectorized)
		# Apply temperature scaling for better calibration
		scaled_logits = logits / torch.clamp(self.temperature, min=0.1, max=5.0)
		probs = F.softmax(scaled_logits, dim=-1)

		# Vectorized entropy computation
		log_probs = F.log_softmax(scaled_logits, dim=-1)
		entropy = -torch.sum(probs * log_probs, dim=-1)
		max_entropy = math.log(probs.size(-1))
		normalized_entropy = 1.0 - (entropy / max_entropy)

		# Method 3: Monte Carlo Dropout uncertainty (vectorized when training)
		if self.training and mc_samples > 1:
			mc_confidences = []
			for _ in range(mc_samples):
				mc_hidden = self.mc_dropout(flat_hidden)
				mc_conf = self.confidence_head(mc_hidden).view(batch_size, seq_len)
				mc_confidences.append(mc_conf)

			mc_stack = torch.stack(mc_confidences)  # [S, B, L]
			mc_mean = mc_stack.mean(dim=0)
			mc_std = mc_stack.std(dim=0)
			mc_confidence = mc_mean * torch.exp(-mc_std)  # Lower confidence for high variance
		else:
			mc_confidence = direct_confidence

		# Combine confidences using calibration network if available
		if self.calibration_net is not None:
			# Stack confidence sources
			confidence_features = torch.stack([
				direct_confidence,
				normalized_entropy,
				mc_confidence
			], dim=-1)  # [B, L, 3]

			flat_features = confidence_features.view(-1, 3)
			calibrated_confidence = self.calibration_net(flat_features)
			final_confidence = calibrated_confidence.view(batch_size, seq_len)
		else:
			# Simple weighted average
			final_confidence = (direct_confidence + normalized_entropy + mc_confidence) / 3.0

		# Numerical stability
		final_confidence = torch.clamp(final_confidence, 0.01, 0.99)

		# Ensure output is always float type
		if final_confidence.dtype in [torch.long, torch.int, torch.int32, torch.int64]:
			final_confidence = final_confidence.float()

		return final_confidence

	def compute_sequence_confidence_batch(self, token_confidences: torch.Tensor,
										attention_weights: Optional[List[torch.Tensor]] = None) -> torch.Tensor:
		"""Vectorized sequence-level confidence aggregation"""
		validate_tensor_shape(token_confidences, 2, "token_confidences")

		if attention_weights is not None and len(attention_weights) > 0:
			# Use attention weights from last layer for importance weighting
			last_attn = attention_weights[-1]  # [B, H, L, L]

			# Average across heads and get diagonal (self-attention strength)
			self_attention = last_attn.mean(dim=1).diagonal(dim1=-2, dim2=-1)  # [B, L]

			# Weighted confidence
			weights = F.softmax(self_attention, dim=-1)
			weighted_confidence = (token_confidences * weights).sum(dim=-1)
		else:
			# Simple average
			weighted_confidence = token_confidences.mean(dim=-1)

		return weighted_confidence

	def update_temperature(self, logits: torch.Tensor, targets: torch.Tensor) -> None:
		"""Update temperature parameter for better calibration"""
		if not self.training:
			return

		# Temperature scaling optimization (simplified)
		with torch.no_grad():
			scaled_logits = logits / self.temperature
			nll = F.cross_entropy(scaled_logits.view(-1, scaled_logits.size(-1)),
								targets.view(-1), ignore_index=-100)

			# Simple gradient step for temperature
			if nll.item() > 2.0:  # Poor calibration
				self.temperature.data *= 1.1
			elif nll.item() < 0.5:  # Over-confident
				self.temperature.data *= 0.9

			# Clamp temperature to reasonable range
			self.temperature.data.clamp_(0.5, 3.0)

class DiffusionPhase(Enum):
	"""Enumeration for diffusion phases"""
	STRUCTURE = "structure"
	DETAIL = "detail"
	REFINEMENT = "refinement"

@dataclass
class DiffusionState:
	"""State container for diffusion process"""
	sequence: torch.Tensor
	hidden_states: torch.Tensor
	confidence_scores: torch.Tensor
	attention_weights: List[torch.Tensor]
	memory_context: Optional[torch.Tensor]
	step: int
	phase: DiffusionPhase
	edit_history: List[Dict[str, torch.Tensor]]
	quality_score: float

class ProgressiveRefiner(nn.Module):
	"""Manages progressive refinement strategy"""

	def __init__(self, config: DiffusionConfig):
		super().__init__()
		self.config = config
		self.structure_threshold = int(config.diffusion_steps * 0.5)  # First 50% for structure
		self.detail_threshold = int(config.diffusion_steps * 0.8)     # Next 30% for detail

		# Phase-specific attention modifiers
		self.structure_modifier = nn.Parameter(torch.ones(config.num_heads))
		self.detail_modifier = nn.Parameter(torch.ones(config.num_heads))
		self.refinement_modifier = nn.Parameter(torch.ones(config.num_heads))

	def get_current_phase(self, step: int) -> DiffusionPhase:
		"""Determine current refinement phase"""
		if step < self.structure_threshold:
			return DiffusionPhase.STRUCTURE
		elif step < self.detail_threshold:
			return DiffusionPhase.DETAIL
		else:
			return DiffusionPhase.REFINEMENT

	def get_phase_params(self, phase: DiffusionPhase) -> Dict[str, Any]:
		"""Get phase-specific parameters"""
		if phase == DiffusionPhase.STRUCTURE:
			return {
				"edit_ratio": 0.3,  # Edit more tokens in structure phase
				"temperature_boost": 1.2,
				"attention_modifier": self.structure_modifier,
				"focus": "global_coherence"
			}
		elif phase == DiffusionPhase.DETAIL:
			return {
				"edit_ratio": 0.15,  # Moderate editing for details
				"temperature_boost": 1.0,
				"attention_modifier": self.detail_modifier,
				"focus": "local_coherence"
			}
		else:  # REFINEMENT
			return {
				"edit_ratio": 0.05,  # Minimal editing for refinement
				"temperature_boost": 0.8,
				"attention_modifier": self.refinement_modifier,
				"focus": "polish"
			}

class ConsistencyChecker(nn.Module):
	"""Validates consistency between diffusion steps"""

	def __init__(self, config: DiffusionConfig):
		super().__init__()
		self.config = config

		# Consistency scoring networks
		self.semantic_checker = nn.Sequential(
			nn.Linear(config.hidden_dim * 2, config.hidden_dim),
			nn.ReLU(),
			nn.Linear(config.hidden_dim, 1),
			nn.Sigmoid()
		)

		self.structural_checker = nn.Sequential(
			nn.Linear(config.hidden_dim, config.hidden_dim // 2),
			nn.ReLU(),
			nn.Linear(config.hidden_dim // 2, 1),
			nn.Sigmoid()
		)

		# Thresholds for consistency
		self.semantic_threshold = 0.7
		self.structural_threshold = 0.8

	def check_step_consistency(self, prev_state: DiffusionState,
							 curr_state: DiffusionState) -> Dict[str, float]:
		"""Check consistency between consecutive diffusion steps"""
		prev_hidden = prev_state.hidden_states
		curr_hidden = curr_state.hidden_states

		# Semantic consistency check
		combined_hidden = torch.cat([prev_hidden.mean(dim=1), curr_hidden.mean(dim=1)], dim=-1)
		semantic_score = self.semantic_checker(combined_hidden).mean().item()

		# Structural consistency (sequence-level)
		prev_struct = self.structural_checker(prev_hidden.mean(dim=1)).mean().item()
		curr_struct = self.structural_checker(curr_hidden.mean(dim=1)).mean().item()
		structural_score = 1.0 - abs(prev_struct - curr_struct)

		# Token-level cosine similarity
		prev_norm = F.normalize(prev_hidden, dim=-1)
		curr_norm = F.normalize(curr_hidden, dim=-1)
		cosine_sim = torch.sum(prev_norm * curr_norm, dim=-1).mean().item()

		return {
			"semantic_consistency": semantic_score,
			"structural_consistency": structural_score,
			"cosine_similarity": cosine_sim,
			"overall_consistency": (semantic_score + structural_score + cosine_sim) / 3.0
		}

	def is_consistent(self, consistency_scores: Dict[str, float]) -> bool:
		"""Determine if step is consistent enough"""
		return (consistency_scores["semantic_consistency"] >= self.semantic_threshold and
				consistency_scores["structural_consistency"] >= self.structural_threshold)

class EarlyStoppingMonitor(nn.Module):
	"""Monitors quality and decides on early stopping"""

	def __init__(self, config: DiffusionConfig, patience: int = 3, min_improvement: float = 0.01):
		super().__init__()
		self.config = config
		self.patience = patience
		self.min_improvement = min_improvement

		# Quality estimation network
		self.quality_estimator = nn.Sequential(
			nn.Linear(config.hidden_dim, config.hidden_dim // 2),
			nn.ReLU(),
			nn.Linear(config.hidden_dim // 2, 1),
			nn.Sigmoid()
		)

		self.reset()

	def reset(self):
		"""Reset monitoring state"""
		self.quality_history = []
		self.best_quality = float('-inf')
		self.steps_without_improvement = 0

	def estimate_quality(self, state: DiffusionState) -> float:
		"""Estimate current generation quality"""
		# Combine multiple quality indicators
		hidden_quality = self.quality_estimator(state.hidden_states.mean(dim=1)).mean().item()
		confidence_quality = state.confidence_scores.mean().item()

		# Penalize inconsistency in confidence scores
		confidence_std = state.confidence_scores.std().item()
		consistency_penalty = min(confidence_std, 0.2)

		overall_quality = (hidden_quality + confidence_quality - consistency_penalty) / 2.0
		return max(0.0, min(1.0, overall_quality))

	def should_stop(self, state: DiffusionState, min_steps: int = 3) -> Tuple[bool, str]:
		"""Determine if early stopping should be triggered"""
		current_quality = self.estimate_quality(state)
		self.quality_history.append(current_quality)

		# Always continue for minimum steps
		if state.step < min_steps:
			return False, "minimum_steps_not_reached"

		# Check for improvement
		if current_quality > self.best_quality + self.min_improvement:
			self.best_quality = current_quality
			self.steps_without_improvement = 0
			return False, "improving"
		else:
			self.steps_without_improvement += 1

			if self.steps_without_improvement >= self.patience:
				return True, "no_improvement"

		# Check for quality degradation trend
		if len(self.quality_history) >= 3:
			recent_trend = self.quality_history[-3:]
			if all(recent_trend[i] < recent_trend[i-1] for i in range(1, len(recent_trend))):
				return True, "quality_degradation"

		return False, "continuing"

class BranchManager(nn.Module):
	"""Manages multiple diffusion branches"""

	def __init__(self, config: DiffusionConfig, max_branches: int = 3):
		super().__init__()
		self.config = config
		self.max_branches = max_branches

		# Branch scoring network
		self.branch_scorer = nn.Sequential(
			nn.Linear(config.hidden_dim, config.hidden_dim // 2),
			nn.ReLU(),
			nn.Linear(config.hidden_dim // 2, 1),
			nn.Sigmoid()
		)

		self.active_branches: Dict[str, DiffusionState] = {}

	def create_branch(self, parent_state: DiffusionState, strategy: str) -> str:
		"""Create new branch from parent state"""
		if len(self.active_branches) >= self.max_branches:
			# Remove worst branch
			worst_branch = self._find_worst_branch()
			if worst_branch:
				del self.active_branches[worst_branch]

		branch_id = f"branch_{len(self.active_branches)}_{strategy}"

		# Deep copy state for new branch
		new_state = DiffusionState(
			sequence=parent_state.sequence.clone(),
			hidden_states=parent_state.hidden_states.clone(),
			confidence_scores=parent_state.confidence_scores.clone(),
			attention_weights=[w.clone() for w in parent_state.attention_weights],
			memory_context=parent_state.memory_context.clone() if parent_state.memory_context is not None else None,
			step=parent_state.step,
			phase=parent_state.phase,
			edit_history=parent_state.edit_history.copy(),
			quality_score=parent_state.quality_score
		)

		self.active_branches[branch_id] = new_state
		return branch_id

	def score_branch(self, state: DiffusionState) -> float:
		"""Score branch quality"""
		hidden_score = self.branch_scorer(state.hidden_states.mean(dim=1)).mean().item()
		confidence_score = state.confidence_scores.mean().item()
		return (hidden_score + confidence_score) / 2.0

	def _find_worst_branch(self) -> Optional[str]:
		"""Find the worst performing branch"""
		if not self.active_branches:
			return None

		scores = {bid: self.score_branch(state) for bid, state in self.active_branches.items()}
		return min(scores.keys(), key=lambda k: scores[k])

	def select_best_branch(self) -> Optional[DiffusionState]:
		"""Select the best branch"""
		if not self.active_branches:
			return None

		scores = {bid: self.score_branch(state) for bid, state in self.active_branches.items()}
		best_branch_id = max(scores.keys(), key=lambda k: scores[k])
		return self.active_branches[best_branch_id]

class DiffusionLLM(nn.Module):
	"""Main Diffusion Language Model integrating all components"""

	def __init__(self, config: DiffusionConfig):
		super().__init__()
		self.config = config

		# Core components
		self.transformer = BaseTransformer(config)
		self.memory_manager = MemoryManager(config)
		self.confidence_estimator = ConfidenceEstimator(config)
		self.token_editor = TokenEditor(config)
		self.scheduler = DiffusionScheduler(config)

		# Diffusion-specific components
		self.progressive_refiner = ProgressiveRefiner(config)
		self.consistency_checker = ConsistencyChecker(config)
		self.early_stopping = EarlyStoppingMonitor(config)
		self.branch_manager = BranchManager(config)

		# Mode flags
		self.diffusion_mode = True

	def forward(self, input_ids: torch.Tensor,
				attention_mask: Optional[torch.Tensor] = None,
				memory_states: Optional[torch.Tensor] = None,
				step: Optional[int] = None,
				max_steps: Optional[int] = None,
				use_diffusion: bool = True,
				return_all_steps: bool = False) -> Dict[str, Any]:
		"""
		Forward pass supporting both normal and diffusion modes

		Args:
			input_ids: Input token sequences [B, L]
			attention_mask: Attention mask [B, L]
			memory_states: External memory context [B, M, H]
			step: Current diffusion step (if in diffusion mode)
			max_steps: Maximum diffusion steps
			use_diffusion: Whether to use diffusion process
			return_all_steps: Whether to return intermediate states

		Returns:
			Dictionary containing outputs and intermediate states
		"""
		validate_tensor_shape(input_ids, 2, "input_ids")

		if not use_diffusion or max_steps is None:
			# Standard transformer forward pass
			return self._standard_forward(input_ids, attention_mask, memory_states)
		else:
			# Diffusion forward pass
			return self._diffusion_forward(input_ids, attention_mask, memory_states,
										 max_steps, return_all_steps)

	def _standard_forward(self, input_ids: torch.Tensor,
						 attention_mask: Optional[torch.Tensor] = None,
						 memory_states: Optional[torch.Tensor] = None) -> Dict[str, Any]:
		"""Standard transformer forward pass"""

		# Get memory context
		if memory_states is None:
			# Create dummy hidden states for memory query
			dummy_hidden = torch.randn(input_ids.size(0), input_ids.size(1),
									 self.config.hidden_dim, device=input_ids.device)
			memory_context = self.memory_manager.read_memory_context(dummy_hidden)
		else:
			memory_context = memory_states

		# Forward through transformer
		outputs = self.transformer(
			input_ids=input_ids,
			memory_states=memory_context,
			attention_mask=attention_mask,
			use_cache=self.config.use_kv_cache
		)

		# Estimate confidence
		confidence_scores = self.confidence_estimator.estimate_token_confidence_batch(
			outputs["hidden_states"], outputs["logits"]
		)
		# Ensure confidence scores are float type
		if confidence_scores.dtype in [torch.long, torch.int, torch.int32, torch.int64]:
			confidence_scores = confidence_scores.float()

		# Update memory with current context
		self.memory_manager.update_memories(input_ids, outputs["hidden_states"], 0)

		return {
			"logits": outputs["logits"],
			"hidden_states": outputs["hidden_states"],
			"confidence_scores": confidence_scores,
			"attention_weights": outputs["attention_weights"],
			"memory_context": memory_context
		}

	def _diffusion_forward(self, input_ids: torch.Tensor,
						  attention_mask: Optional[torch.Tensor] = None,
						  memory_states: Optional[torch.Tensor] = None,
						  max_steps: int = 14,
						  return_all_steps: bool = False) -> Dict[str, Any]:
		"""Memory-optimized multi-step diffusion forward pass"""

		batch_size, seq_len = input_ids.shape
		device = input_ids.device

		# Initialize diffusion state with memory optimization
		current_sequence = input_ids.clone()
		all_states = [] if return_all_steps else None

		# Reset monitoring components
		self.early_stopping.reset()
		self.branch_manager.active_branches.clear()

		# Pre-allocate tensors to avoid repeated allocation
		hidden_states_buffer = None
		confidence_buffer = None

		# Track final state for return
		final_state = None
		stop_reason = "max_steps"

		for step in range(max_steps):
			# Get current phase and parameters
			current_phase = self.progressive_refiner.get_current_phase(step)
			phase_params = self.progressive_refiner.get_phase_params(current_phase)

			# Memory-efficient memory context retrieval
			if step == 0 or step % 3 == 0:  # Only update memory context every few steps
				# Use actual hidden states instead of random tensor
				if hidden_states_buffer is not None:
					query_states = hidden_states_buffer.detach()
				else:
					query_states = torch.zeros(batch_size, seq_len, self.config.hidden_dim,
											 device=device, dtype=input_ids.dtype)
				memory_context = self.memory_manager.read_memory_context(query_states)

			# Forward pass through transformer with memory optimization
			outputs = self.transformer(
				input_ids=current_sequence,
				memory_states=memory_context,
				attention_mask=attention_mask,
				use_cache=self.config.use_kv_cache and step > 0
			)

			# Reuse buffers to avoid memory allocation
			if hidden_states_buffer is None:
				hidden_states_buffer = outputs["hidden_states"].clone()
			else:
				hidden_states_buffer.copy_(outputs["hidden_states"])

			# Estimate confidence with buffer reuse
			if confidence_buffer is None:
				confidence_scores = self.confidence_estimator.estimate_token_confidence_batch(
					hidden_states_buffer, outputs["logits"]
				)
				# Ensure confidence scores are float type
				if confidence_scores.dtype in [torch.long, torch.int, torch.int32, torch.int64]:
					confidence_scores = confidence_scores.float()
				confidence_buffer = confidence_scores.clone()
			else:
				confidence_scores = self.confidence_estimator.estimate_token_confidence_batch(
					hidden_states_buffer, outputs["logits"]
				)
				# Ensure confidence scores are float type
				if confidence_scores.dtype in [torch.long, torch.int, torch.int32, torch.int64]:
					confidence_scores = confidence_scores.float()
				confidence_buffer.copy_(confidence_scores)

			# Create current state (only store essential data)
			current_state = DiffusionState(
				sequence=current_sequence.clone() if return_all_steps else current_sequence,
				hidden_states=hidden_states_buffer.clone() if return_all_steps else hidden_states_buffer,
				confidence_scores=confidence_buffer.clone() if return_all_steps else confidence_buffer,
				attention_weights=outputs["attention_weights"] if return_all_steps else None,
				memory_context=memory_context if return_all_steps else None,
				step=step,
				phase=current_phase,
				edit_history=[],
				quality_score=0.0
			)

			# Update memory with current step (less frequently to save memory)
			if step % 2 == 0:  # Update every other step
				self.memory_manager.update_memories(current_sequence, hidden_states_buffer, step)

			# Simplified consistency checking to save memory
			if step > 0 and return_all_steps and all_states:
				consistency_scores = self.consistency_checker.check_step_consistency(
					all_states[-1], current_state
				)

				# Simplified branching logic
				if not self.consistency_checker.is_consistent(consistency_scores):
					if len(self.branch_manager.active_branches) < 2:  # Limit branches to save memory
						self.branch_manager.create_branch(all_states[-1], "alternative")

			# Store current state only if needed
			if return_all_steps:
				all_states.append(current_state)
			else:
				# Keep only the final state
				final_state = current_state

			# Check for early stopping
			should_stop, stop_reason = self.early_stopping.should_stop(current_state)
			if should_stop and step >= 3:  # Minimum 3 steps
				break

			# Skip editing on final step
			if step >= max_steps - 1:
				break

			# Perform token editing for next step
			try:
				current_sequence = self._perform_token_editing(
					current_state, phase_params, step
				)
			except RuntimeError as e:
				if "out of memory" in str(e).lower():
					# Fallback: skip editing and continue
					break
				else:
					raise e

			# Clear intermediate tensors to free memory
			if not return_all_steps:
				del outputs  # Free transformer outputs

		# Use final state if not storing all states
		if not return_all_steps and final_state is not None:
			current_state = final_state

		# Select best branch if multiple exist (simplified)
		if self.branch_manager.active_branches:
			best_branch_state = self.branch_manager.select_best_branch()
			if best_branch_state:
				current_state = best_branch_state

		# Final outputs with memory optimization
		result = {
			"logits": current_state.hidden_states @ self.transformer.lm_head.weight.T,
			"hidden_states": current_state.hidden_states,
			"confidence_scores": current_state.confidence_scores,
			"attention_weights": current_state.attention_weights if current_state.attention_weights is not None else [],
			"memory_context": current_state.memory_context,
			"final_sequence": current_state.sequence,
			"num_steps": step + 1,
			"stop_reason": stop_reason
		}

		if return_all_steps and all_states:
			result["all_states"] = all_states

		return result

	def _perform_token_editing(self, state: DiffusionState,
							  phase_params: Dict[str, Any],
							  step: int) -> torch.Tensor:
		"""Perform confidence-guided token editing"""

		# Determine number of tokens to edit
		base_edit_count = self.scheduler.get_edit_count(step, state.sequence.size(1))
		phase_edit_count = int(base_edit_count * phase_params["edit_ratio"])
		edit_count = max(1, phase_edit_count)

		# Select tokens to edit based on confidence
		edit_positions = self._select_edit_positions(state, edit_count, phase_params)

		# Generate new tokens
		temperature = self.scheduler.get_temperature(step) * phase_params["temperature_boost"]
		new_tokens = self.token_editor.generate_replacement_tokens_batch(
			state.hidden_states, edit_positions, temperature
		)

		# Apply edits
		edited_sequence = self.token_editor.replace_tokens_vectorized(
			state.sequence, edit_positions, new_tokens
		)

		# Store edit history
		edit_record = {
			"step": step,
			"positions": edit_positions,
			"old_tokens": torch.gather(state.sequence, 1, edit_positions),
			"new_tokens": new_tokens,
			"confidence_scores": torch.gather(state.confidence_scores, 1, edit_positions),
			"phase": state.phase.value
		}
		state.edit_history.append(edit_record)

		return edited_sequence

	def _select_edit_positions(self, state: DiffusionState, edit_count: int,
							  phase_params: Dict[str, Any]) -> torch.Tensor:
		"""Select positions to edit based on phase and confidence"""

		batch_size, seq_len = state.sequence.shape

		if phase_params["focus"] == "global_coherence":
			# Structure phase: focus on sentence boundaries and high-level structure
			# Boost confidence scores at sentence boundaries (periods, etc.)
			sentence_boundary_boost = torch.zeros_like(state.confidence_scores)
			period_positions = (state.sequence == 13) | (state.sequence == 30)  # Common period token IDs
			sentence_boundary_boost[period_positions] -= 0.2  # Lower confidence at boundaries

			adjusted_confidence = state.confidence_scores + sentence_boundary_boost

		elif phase_params["focus"] == "local_coherence":
			# Detail phase: focus on word choice and local flow
			adjusted_confidence = state.confidence_scores

		else:  # polish
			# Refinement phase: very conservative editing
			adjusted_confidence = state.confidence_scores + 0.1  # Boost all confidences

		# Use attention selector for intelligent position selection
		attention_selector = AttentionSelector(self.config)
		edit_positions = attention_selector.select_tokens_to_edit(
			state.hidden_states, adjusted_confidence, edit_count
		)

		return edit_positions

	def generate(self, input_ids: torch.Tensor,
				max_length: int = 100,
				max_diffusion_steps: int = 14,
				temperature: float = 1.0,
				do_sample: bool = True,
				use_diffusion: bool = True) -> Dict[str, Any]:
		"""Generate text using diffusion refinement"""

		self.eval()
		batch_size, input_length = input_ids.shape
		device = input_ids.device

		generated_ids = input_ids.clone()
		generation_steps = []

		with torch.no_grad():
			for gen_step in range(max_length):
				if use_diffusion:
					# Use diffusion for each generated token
					outputs = self._diffusion_forward(
						generated_ids,
						max_steps=max_diffusion_steps,
						return_all_steps=True
					)

					logits = outputs["logits"][:, -1, :]  # Last position logits

				else:
					# Standard autoregressive generation
					outputs = self._standard_forward(generated_ids)
					logits = outputs["logits"][:, -1, :]

				# Sample next token
				if do_sample:
					probs = F.softmax(logits / temperature, dim=-1)
					next_token = torch.multinomial(probs, num_samples=1)
				else:
					next_token = torch.argmax(logits, dim=-1, keepdim=True)

				# Append to sequence
				generated_ids = torch.cat([generated_ids, next_token], dim=-1)

				# Store generation step info
				generation_steps.append({
					"step": gen_step,
					"token": next_token.item(),
					"confidence": outputs.get("confidence_scores", torch.tensor([0.5]))[:, -1].mean().item(),
					"diffusion_steps": outputs.get("num_steps", 1)
				})

				# Check for EOS token
				if next_token.item() in [2, 50256]:  # Common EOS token IDs
					break

		return {
			"generated_ids": generated_ids,
			"generation_steps": generation_steps,
			"input_length": input_length,
			"total_length": generated_ids.size(1)
		}

	def set_diffusion_mode(self, mode: bool):
		"""Enable/disable diffusion mode"""
		self.diffusion_mode = mode

	def get_memory_states(self) -> Dict[str, torch.Tensor]:
		"""Get current memory states for inspection"""
		return {
			"short_term_utilization": self.memory_manager.short_term.get_utilization(),
			"medium_term_utilization": self.memory_manager.medium_term.get_utilization(),
			"long_term_size": self.memory_manager.long_term.filled.item()
		}

	def clear_memory(self):
		"""Clear all memory states"""
		self.memory_manager.short_term.position = torch.tensor(0)
		self.memory_manager.short_term.filled = torch.tensor(0)
		self.memory_manager.medium_term.position = torch.tensor(0)
		self.memory_manager.medium_term.filled = torch.tensor(0)
		self.memory_manager.long_term.position = torch.tensor(0)
		self.memory_manager.long_term.filled = torch.tensor(0)
