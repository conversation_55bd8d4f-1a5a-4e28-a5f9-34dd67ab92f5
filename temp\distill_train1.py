

import asyncio
import json
import logging
import time
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, AsyncGenerator
from dataclasses import dataclass, field
from collections import defaultdict, deque
import random

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import requests
import aiohttp
import numpy as np
from tqdm.asyncio import tqdm as atqdm
from tqdm import tqdm
import wandb

# Import our existing components
from model import DiffusionLLM, DiffusionConfig, validate_tensor_shape
from train import TrainingConfig, DiffusionTrainer, DiffusionLoss, setup_logging

# ============================================================================
# FUNDAMENTAL COMPONENT 1: PARENT MODEL INTERFACE
# ============================================================================

@dataclass
class ParentModelConfig:
	"""Configuration for the parent model (DeepSeek-R1)"""
	model_name: str = "DeepSeek-R1-0528-Qwen3-8B-BF16:latest"
	ollama_host: str = "http://localhost:11434"
	max_tokens: int = 2048
	temperature: float = 0.7
	top_p: float = 0.9
	timeout: int = 120
	max_retries: int = 3
	concurrent_requests: int = 4

	# Generation parameters for different types of tasks
	reasoning_temperature: float = 0.8  # Higher for creative reasoning
	factual_temperature: float = 0.3    # Lower for factual responses
	creative_temperature: float = 1.0   # Highest for creative tasks

class OllamaClient:
	"""
	Asynchronous client for communicating with Ollama DeepSeek-R1 model.

	This class handles:
	- Connection management to local Ollama instance
	- Request/response parsing with proper error handling
	- Rate limiting and concurrent request management
	- Automatic retry logic for failed requests
	"""

	def __init__(self, config: ParentModelConfig):
		self.config = config
		self.session: Optional[aiohttp.ClientSession] = None
		self.request_semaphore = asyncio.Semaphore(config.concurrent_requests)
		self.request_count = 0
		self.successful_requests = 0
		self.failed_requests = 0

		# Setup logging
		self.logger = logging.getLogger(f"{__name__}.OllamaClient")

	async def __aenter__(self):
		"""Async context manager entry"""
		connector = aiohttp.TCPConnector(limit=20, limit_per_host=10)
		timeout = aiohttp.ClientTimeout(total=self.config.timeout)
		self.session = aiohttp.ClientSession(connector=connector, timeout=timeout)

		# Test connection
		await self._test_connection()
		return self

	async def __aexit__(self, exc_type, exc_val, exc_tb):
		"""Async context manager exit"""
		if self.session:
			await self.session.close()

	async def _test_connection(self) -> None:
		"""Test connection to Ollama and verify model availability"""
		try:
			# Test basic connection
			async with self.session.get(f"{self.config.ollama_host}/api/tags") as response:
				if response.status != 200:
					raise ConnectionError(f"Ollama not accessible: HTTP {response.status}")

				models = await response.json()
				model_names = [model["name"] for model in models.get("models", [])]

				if self.config.model_name not in model_names:
					self.logger.warning(
						f"Model {self.config.model_name} not found. Available models: {model_names}"
					)

			self.logger.info(f"Successfully connected to Ollama at {self.config.ollama_host}")

		except Exception as e:
			raise ConnectionError(f"Failed to connect to Ollama: {str(e)}")

	async def generate_response(self, prompt: str,
							  task_type: str = "general",
							  system_prompt: Optional[str] = None) -> Dict[str, Any]:
		"""
		Generate response from DeepSeek-R1 with task-specific parameters.

		Args:
			prompt: The input prompt for the model
			task_type: Type of task (reasoning, factual, creative, general)
			system_prompt: Optional system prompt to guide behavior

		Returns:
			Dict containing response, thoughts, and metadata
		"""
		async with self.request_semaphore:
			for attempt in range(self.config.max_retries):
				try:
					# Select temperature based on task type
					temperature = {
						"reasoning": self.config.reasoning_temperature,
						"factual": self.config.factual_temperature,
						"creative": self.config.creative_temperature,
						"general": self.config.temperature
					}.get(task_type, self.config.temperature)

					# Prepare request payload
					payload = {
						"model": self.config.model_name,
						"prompt": prompt,
						"system": system_prompt or "",
						"stream": False,
						"options": {
							"temperature": temperature,
							"top_p": self.config.top_p,
							"num_predict": self.config.max_tokens,
						}
					}

					start_time = time.time()

					async with self.session.post(
						f"{self.config.ollama_host}/api/generate",
						json=payload
					) as response:

						if response.status != 200:
							error_text = await response.text()
							raise aiohttp.ClientError(f"HTTP {response.status}: {error_text}")

						result = await response.json()
						generation_time = time.time() - start_time

						# Parse the response
						parsed_response = self._parse_deepseek_response(result["response"])

						self.request_count += 1
						self.successful_requests += 1

						return {
							"raw_response": result["response"],
							"thoughts": parsed_response["thoughts"],
							"output": parsed_response["output"],
							"task_type": task_type,
							"temperature": temperature,
							"generation_time": generation_time,
							"tokens_generated": len(result["response"].split()),
							"model": self.config.model_name,
							"attempt": attempt + 1
						}

				except Exception as e:
					self.failed_requests += 1
					self.logger.warning(f"Request attempt {attempt + 1} failed: {str(e)}")

					if attempt == self.config.max_retries - 1:
						raise

					# Exponential backoff
					await asyncio.sleep(2 ** attempt)

		raise RuntimeError("All retry attempts exhausted")

	def _parse_deepseek_response(self, response: str) -> Dict[str, str]:
		"""
		Parse DeepSeek-R1 response to extract thoughts and output.

		The model generates responses in the format:
		<think>reasoning process here</think>
		Final output here
		"""
		thoughts = ""
		output = response

		# Extract thoughts using regex
		think_pattern = r"<think>(.*?)</think>"
		think_matches = re.findall(think_pattern, response, re.DOTALL)

		if think_matches:
			# Join all thinking sections
			thoughts = "\n".join(match.strip() for match in think_matches)

			# Remove think tags from output
			output = re.sub(think_pattern, "", response, flags=re.DOTALL).strip()

		return {
			"thoughts": thoughts,
			"output": output
		}

	def get_statistics(self) -> Dict[str, Any]:
		"""Get client usage statistics"""
		success_rate = self.successful_requests / max(self.request_count, 1)
		return {
			"total_requests": self.request_count,
			"successful_requests": self.successful_requests,
			"failed_requests": self.failed_requests,
			"success_rate": success_rate
		}

# ============================================================================
# FUNDAMENTAL COMPONENT 2: DATA GENERATION PIPELINE
# ============================================================================

@dataclass
class DataGenerationConfig:
	"""Configuration for training data generation"""
	num_examples_per_category: int = 1000
	max_concurrent_generations: int = 4
	output_dir: str = "./generated_data"
	save_interval: int = 100

	# Task distribution weights
	reasoning_weight: float = 0.3
	factual_weight: float = 0.2
	creative_weight: float = 0.2
	instruction_weight: float = 0.2
	conversation_weight: float = 0.1

	# Quality filtering
	min_thought_length: int = 50  # Minimum characters in thoughts
	min_output_length: int = 20   # Minimum characters in output
	max_output_length: int = 1000 # Maximum characters in output

class TaskGenerator:
	"""
	Generates diverse prompts for training the child model.

	This class creates various types of tasks that encourage
	the parent model to demonstrate different reasoning patterns
	and knowledge domains.
	"""

	def __init__(self):
		self.reasoning_prompts = [
			"Solve this step by step: {problem}",
			"Analyze the following situation and provide your reasoning: {scenario}",
			"Compare and contrast these concepts, explaining your thought process: {concepts}",
			"What would happen if {hypothetical}? Think through the implications.",
			"Explain why {statement} is true or false, showing your reasoning.",
		]

		self.factual_prompts = [
			"Explain the concept of {topic} in detail.",
			"What are the key facts about {subject}?",
			"Describe the process of {process}.",
			"List and explain the main features of {item}.",
			"What is the relationship between {concept_a} and {concept_b}?",
		]

		self.creative_prompts = [
			"Write a short story about {theme}.",
			"Create a dialogue between {character_a} and {character_b} discussing {topic}.",
			"Imagine you are {role}. How would you approach {situation}?",
			"Generate creative solutions for {problem}.",
			"Describe {scene} in vivid, creative language.",
		]

		self.instruction_prompts = [
			"How do I {task}? Provide step-by-step instructions.",
			"What's the best way to {goal}?",
			"Can you help me understand {concept}?",
			"I need advice on {situation}. What should I do?",
			"Explain how to {skill} for a beginner.",
		]

		# Sample content for filling templates
		self.content_samples = {
			"problems": [
				"A train travels at 80 mph for 2 hours, then at 60 mph for 3 hours. What's the average speed?",
				"If 3x + 7 = 22, what is the value of x?",
				"A company's profit increases by 15% each year. If they made $100,000 this year, what will they make in 3 years?",
			],
			"scenarios": [
				"A team project is behind schedule and team members are blaming each other",
				"You find a wallet on the street with cash and ID",
				"Your friend asks to borrow money but has never paid you back before",
			],
			"concepts": [
				"democracy and authoritarianism",
				"artificial intelligence and human intelligence",
				"renewable energy and fossil fuels",
			],
			"topics": [
				"quantum computing", "photosynthesis", "blockchain technology",
				"climate change", "machine learning", "supply chain management",
			],
			"themes": [
				"time travel", "first contact with aliens", "a world without internet",
				"life in a floating city", "artificial intelligence gaining consciousness",
			],
			"tasks": [
				"start a vegetable garden", "learn a new language", "improve my sleep quality",
				"organize my digital files", "build better habits",
			]
		}

	def generate_prompt(self, task_type: str) -> Tuple[str, str]:
		"""
		Generate a prompt of the specified type.

		Returns:
			Tuple of (prompt, expected_task_type)
		"""
		if task_type == "reasoning":
			template = random.choice(self.reasoning_prompts)
			if "{problem}" in template:
				content = random.choice(self.content_samples["problems"])
				prompt = template.format(problem=content)
			elif "{scenario}" in template:
				content = random.choice(self.content_samples["scenarios"])
				prompt = template.format(scenario=content)
			elif "{concepts}" in template:
				content = random.choice(self.content_samples["concepts"])
				prompt = template.format(concepts=content)
			else:
				# Handle other placeholders
				prompt = template.format(
					hypothetical="social media suddenly disappeared",
					statement="exercise improves mental health"
				)

		elif task_type == "factual":
			template = random.choice(self.factual_prompts)
			content = random.choice(self.content_samples["topics"])
			prompt = template.format(
				topic=content, subject=content, process=content,
				item=content, concept_a=content,
				concept_b=random.choice(self.content_samples["topics"])
			)

		elif task_type == "creative":
			template = random.choice(self.creative_prompts)
			theme = random.choice(self.content_samples["themes"])
			prompt = template.format(
				theme=theme, character_a="a scientist", character_b="an artist",
				topic=random.choice(self.content_samples["topics"]),
				role="a detective", situation="a mysterious disappearance",
				problem="reducing plastic waste", scene="a bustling marketplace at sunset"
			)

		elif task_type == "instruction":
			template = random.choice(self.instruction_prompts)
			task = random.choice(self.content_samples["tasks"])
			prompt = template.format(
				task=task, goal=task, concept=random.choice(self.content_samples["topics"]),
				situation="career change", skill="public speaking"
			)

		else:  # conversation
			conversation_starters = [
				"I'm feeling overwhelmed with work lately. Any suggestions?",
				"What do you think about the impact of AI on jobs?",
				"I'm trying to decide between two career paths. Can you help me think through it?",
				"How do you stay motivated when working on long-term goals?",
			]
			prompt = random.choice(conversation_starters)

		return prompt, task_type

class DataGenerator:
	"""
	Orchestrates the generation of training data using the parent model.

	This class manages the entire pipeline from prompt generation
	to data validation and storage.
	"""

	def __init__(self, parent_config: ParentModelConfig,
				 generation_config: DataGenerationConfig):
		self.parent_config = parent_config
		self.generation_config = generation_config
		self.task_generator = TaskGenerator()

		# Statistics tracking
		self.generation_stats = defaultdict(int)
		self.quality_stats = defaultdict(int)

		# Setup logging
		self.logger = logging.getLogger(f"{__name__}.DataGenerator")

		# Create output directory
		Path(self.generation_config.output_dir).mkdir(parents=True, exist_ok=True)

	async def generate_training_data(self) -> List[Dict[str, Any]]:
		"""
		Generate comprehensive training dataset using the parent model.

		Returns:
			List of training examples with thoughts and outputs
		"""
		self.logger.info("Starting training data generation...")

		# Calculate task distribution
		task_weights = {
			"reasoning": self.generation_config.reasoning_weight,
			"factual": self.generation_config.factual_weight,
			"creative": self.generation_config.creative_weight,
			"instruction": self.generation_config.instruction_weight,
			"conversation": self.generation_config.conversation_weight,
		}

		# Generate task list
		tasks = []
		total_examples = self.generation_config.num_examples_per_category

		for task_type, weight in task_weights.items():
			num_tasks = int(total_examples * weight)
			tasks.extend([(task_type, i) for i in range(num_tasks)])

		random.shuffle(tasks)

		# Generate data in batches
		generated_data = []

		async with OllamaClient(self.parent_config) as client:
			# Process in batches for better progress tracking
			batch_size = self.generation_config.max_concurrent_generations

			for i in range(0, len(tasks), batch_size):
				batch_tasks = tasks[i:i + batch_size]

				# Generate batch concurrently
				batch_results = await asyncio.gather(
					*[self._generate_single_example(client, task_type, task_id)
					  for task_type, task_id in batch_tasks],
					return_exceptions=True
				)

				# Process results
				for result in batch_results:
					if isinstance(result, Exception):
						self.logger.warning(f"Generation failed: {result}")
						self.generation_stats["failed"] += 1
					elif self._validate_example(result):
						generated_data.append(result)
						self.generation_stats["successful"] += 1
						self.quality_stats[result["task_type"]] += 1
					else:
						self.generation_stats["rejected"] += 1

				# Save intermediate results
				if len(generated_data) % self.generation_config.save_interval == 0:
					await self._save_intermediate_data(generated_data)

				# Progress update
				self.logger.info(
					f"Generated {len(generated_data)} examples "
					f"({len(generated_data) / len(tasks) * 100:.1f}%)"
				)

		# Final save
		await self._save_final_data(generated_data)

		# Log statistics
		client_stats = client.get_statistics()
		self.logger.info(f"Generation complete. Statistics: {self.generation_stats}")
		self.logger.info(f"Client statistics: {client_stats}")
		self.logger.info(f"Quality distribution: {dict(self.quality_stats)}")

		return generated_data

	async def _generate_single_example(self, client: OllamaClient,
									 task_type: str, task_id: int) -> Dict[str, Any]:
		"""Generate a single training example"""
		prompt, _ = self.task_generator.generate_prompt(task_type)

		# Add system prompt to encourage thinking
		system_prompt = (
			"You are an AI assistant that thinks step by step. "
			"Before providing your final answer, use <think>...</think> tags "
			"to show your reasoning process. Be thorough in your thinking."
		)

		response = await client.generate_response(
			prompt=prompt,
			task_type=task_type,
			system_prompt=system_prompt
		)

		return {
			"id": f"{task_type}_{task_id}",
			"prompt": prompt,
			"system_prompt": system_prompt,
			"thoughts": response["thoughts"],
			"output": response["output"],
			"task_type": task_type,
			"generation_metadata": {
				"temperature": response["temperature"],
				"generation_time": response["generation_time"],
				"tokens_generated": response["tokens_generated"],
				"model": response["model"]
			}
		}

	def _validate_example(self, example: Dict[str, Any]) -> bool:
		"""Validate the quality of a generated example"""
		thoughts = example.get("thoughts", "")
		output = example.get("output", "")

		# Length checks
		if len(thoughts) < self.generation_config.min_thought_length:
			return False

		if len(output) < self.generation_config.min_output_length:
			return False

		if len(output) > self.generation_config.max_output_length:
			return False

		# Content quality checks
		if not thoughts.strip() or not output.strip():
			return False

		# Avoid repetitive or low-quality content
		if len(set(thoughts.split())) < len(thoughts.split()) * 0.5:
			return False  # Too repetitive

		return True

	async def _save_intermediate_data(self, data: List[Dict[str, Any]]) -> None:
		"""Save intermediate data to prevent loss"""
		timestamp = int(time.time())
		filepath = Path(self.generation_config.output_dir) / f"intermediate_{timestamp}.jsonl"

		with open(filepath, 'w') as f:
			for example in data[-self.generation_config.save_interval:]:
				f.write(json.dumps(example) + '\n')

	async def _save_final_data(self, data: List[Dict[str, Any]]) -> None:
		"""Save final training data"""
		filepath = Path(self.generation_config.output_dir) / "training_data.jsonl"

		with open(filepath, 'w') as f:
			for example in data:
				f.write(json.dumps(example) + '\n')

		self.logger.info(f"Saved {len(data)} examples to {filepath}")

# ============================================================================
# FUNDAMENTAL COMPONENT 3: THOUGHT PROCESS EXTRACTION
# ============================================================================

class ThoughtProcessor:
	"""
	Processes and analyzes the thought patterns from the parent model.

	This component extracts structured reasoning patterns that can be
	used to train the child model's internal reasoning capabilities.
	"""

	def __init__(self):
		self.reasoning_patterns = {
			"step_by_step": r"(?:first|then|next|finally|step \d+)",
			"cause_effect": r"(?:because|therefore|as a result|consequently)",
			"comparison": r"(?:compared to|unlike|similar to|on the other hand)",
			"evidence": r"(?:for example|evidence shows|studies indicate)",
			"uncertainty": r"(?:might|could|possibly|unclear|uncertain)",
			"conclusion": r"(?:in conclusion|therefore|overall|to summarize)"
		}

	def extract_reasoning_structure(self, thoughts: str) -> Dict[str, Any]:
		"""
		Extract structured reasoning patterns from thought text.

		Args:
			thoughts: Raw thought text from parent model

		Returns:
			Dict containing reasoning structure analysis
		"""
		structure = {
			"raw_thoughts": thoughts,
			"sentence_count": len([s for s in thoughts.split('.') if s.strip()]),
			"reasoning_patterns": {},
			"complexity_score": 0.0,
			"reasoning_steps": [],
			"key_concepts": []
		}

		# Analyze reasoning patterns
		for pattern_name, pattern_regex in self.reasoning_patterns.items():
			matches = re.findall(pattern_regex, thoughts, re.IGNORECASE)
			structure["reasoning_patterns"][pattern_name] = len(matches)

		# Extract reasoning steps
		structure["reasoning_steps"] = self._extract_reasoning_steps(thoughts)

		# Calculate complexity score
		structure["complexity_score"] = self._calculate_complexity(structure)

		# Extract key concepts (simple keyword extraction)
		structure["key_concepts"] = self._extract_key_concepts(thoughts)

		return structure

	def _extract_reasoning_steps(self, thoughts: str) -> List[str]:
		"""Extract individual reasoning steps from thoughts"""
		# Split by common step indicators
		step_indicators = [
			r"First,", r"Second,", r"Third,", r"Next,", r"Then,", r"Finally,",
			r"Step \d+:", r"\d+\.", r"•", r"-"
		]

		steps = []
		lines = thoughts.split('\n')

		for line in lines:
			line = line.strip()
			if not line:
				continue

			# Check if line starts with a step indicator
			is_step = any(re.match(indicator, line, re.IGNORECASE) for indicator in step_indicators)

			if is_step or len(line) > 30:  # Assume substantial lines are steps
				steps.append(line)

		return steps[:10]  # Limit to 10 steps for practicality

	def _calculate_complexity(self, structure: Dict[str, Any]) -> float:
		"""Calculate reasoning complexity score"""
		base_score = structure["sentence_count"] * 0.1

		# Add points for different reasoning patterns
		pattern_bonus = sum(structure["reasoning_patterns"].values()) * 0.2

		# Add points for reasoning steps
		step_bonus = len(structure["reasoning_steps"]) * 0.1

		complexity = base_score + pattern_bonus + step_bonus
		return min(complexity, 10.0)  # Cap at 10

	def _extract_key_concepts(self, thoughts: str) -> List[str]:
		"""Extract key concepts using simple heuristics"""
		# Remove common words and extract meaningful terms
		import re

		# Simple approach: find capitalized words and technical terms
		words = re.findall(r'\b[A-Z][a-z]+\b|\b[a-z]+(?:ing|tion|ness|ment)\b', thoughts)

		# Filter and deduplicate
		concepts = []
		common_words = {'The', 'This', 'That', 'When', 'Where', 'What', 'How', 'Why'}

		for word in words:
			if word not in common_words and len(word) > 3:
				concepts.append(word.lower())

		return list(set(concepts))[:10]  # Return top 10 unique concepts

# ============================================================================
# FUNDAMENTAL COMPONENT 4: KNOWLEDGE DISTILLATION FRAMEWORK
# ============================================================================

class KnowledgeDistillationLoss(nn.Module):
	"""
	Knowledge distillation loss for training child model with parent guidance.

	This loss function combines multiple objectives:
	1. Traditional cross-entropy loss for text generation
	2. Thought alignment loss to match reasoning patterns
	3. Output alignment loss to match final responses
	4. Confidence matching to transfer uncertainty modeling
	"""

	def __init__(self, alpha: float = 0.5, beta: float = 0.3, gamma: float = 0.2,
				 temperature: float = 3.0):
		super().__init__()
		self.alpha = alpha  # Weight for generation loss
		self.beta = beta    # Weight for thought alignment
		self.gamma = gamma  # Weight for output alignment
		self.temperature = temperature  # Distillation temperature

	def forward(self, student_outputs: Dict[str, torch.Tensor],
				teacher_data: Dict[str, Any],
				targets: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
		"""
		Compute knowledge distillation loss.

		Args:
			student_outputs: Outputs from diffusion model
			teacher_data: Processed data from parent model
			targets: Ground truth targets

		Returns:
			Dict of loss components
		"""
		losses = {}

		# 1. Standard generation loss
		student_logits = student_outputs["logits"]
		labels = targets["labels"]

		generation_loss = F.cross_entropy(
			student_logits.view(-1, student_logits.size(-1)),
			labels.view(-1),
			ignore_index=-100
		)
		losses["generation"] = generation_loss

		# 2. Thought alignment loss (if thoughts are available)
		if "thought_embeddings" in teacher_data and "hidden_states" in student_outputs:
			thought_loss = self._compute_thought_alignment_loss(
				student_outputs["hidden_states"],
				teacher_data["thought_embeddings"]
			)
			losses["thought_alignment"] = thought_loss

		# 3. Output alignment loss using teacher logits
		if "teacher_logits" in teacher_data:
			output_loss = self._compute_output_alignment_loss(
				student_logits, teacher_data["teacher_logits"]
			)
			losses["output_alignment"] = output_loss

		# 4. Confidence matching loss
		if "confidence_scores" in student_outputs and "teacher_confidence" in teacher_data:
			confidence_loss = self._compute_confidence_loss(
				student_outputs["confidence_scores"],
				teacher_data["teacher_confidence"]
			)
			losses["confidence_matching"] = confidence_loss

		# Compute weighted total loss
		total_loss = self.alpha * generation_loss

		if "thought_alignment" in losses:
			total_loss += self.beta * losses["thought_alignment"]

		if "output_alignment" in losses:
			total_loss += self.gamma * losses["output_alignment"]

		if "confidence_matching" in losses:
			total_loss += 0.1 * losses["confidence_matching"]

		losses["total"] = total_loss

		return losses

	def _compute_thought_alignment_loss(self, student_hidden: torch.Tensor,
									  teacher_thoughts: torch.Tensor) -> torch.Tensor:
		"""Align student hidden states with teacher thought representations"""
		# Ensure compatible dimensions
		if student_hidden.size(-1) != teacher_thoughts.size(-1):
			# Project to same dimension
			projection = nn.Linear(student_hidden.size(-1), teacher_thoughts.size(-1))
			student_hidden = projection(student_hidden)

		# Compute cosine similarity loss
		student_norm = F.normalize(student_hidden, dim=-1)
		teacher_norm = F.normalize(teacher_thoughts, dim=-1)

		similarity = torch.sum(student_norm * teacher_norm, dim=-1)
		loss = 1.0 - similarity.mean()

		return loss

	def _compute_output_alignment_loss(self, student_logits: torch.Tensor,
									 teacher_logits: torch.Tensor) -> torch.Tensor:
		"""Align student output distribution with teacher"""
		# Apply temperature scaling
		student_soft = F.log_softmax(student_logits / self.temperature, dim=-1)
		teacher_soft = F.softmax(teacher_logits / self.temperature, dim=-1)

		# KL divergence loss
		kl_loss = F.kl_div(student_soft, teacher_soft, reduction='batchmean')

		return kl_loss * (self.temperature ** 2)

	def _compute_confidence_loss(self, student_confidence: torch.Tensor,
							   teacher_confidence: torch.Tensor) -> torch.Tensor:
		"""Match confidence distributions between student and teacher"""
		return F.mse_loss(student_confidence, teacher_confidence)

# ============================================================================
# FUNDAMENTAL COMPONENT 5: TRAINING INTEGRATION
# ============================================================================

@dataclass
class ParentChildTrainingConfig:
	"""Configuration for parent-child training setup"""
	# Parent model config
	parent_config: ParentModelConfig = field(default_factory=ParentModelConfig)

	# Data generation config
	data_generation_config: DataGenerationConfig = field(default_factory=DataGenerationConfig)

	# Child model training config (extends existing TrainingConfig)
	child_training_config: TrainingConfig = field(default_factory=TrainingConfig)

	# Knowledge distillation parameters
	distillation_alpha: float = 0.5  # Generation loss weight
	distillation_beta: float = 0.3   # Thought alignment weight
	distillation_gamma: float = 0.2  # Output alignment weight
	distillation_temperature: float = 3.0

	# Training phases
	use_curriculum: bool = True
	warmup_steps: int = 1000  # Steps with only generation loss
	distillation_start_step: int = 2000  # When to start distillation

	# Data refresh
	regenerate_data_interval: int = 10000  # Regenerate data every N steps
	keep_old_data_ratio: float = 0.3  # Fraction of old data to keep

class ParentChildDataset(Dataset):
	"""
	Dataset that combines generated training data with parent model guidance.

	This dataset:
	1. Loads examples generated by the parent model
	2. Processes thoughts and outputs into training format
	3. Provides both text and structured reasoning data
	"""

	def __init__(self, data_file: str, tokenizer, max_length: int = 1024):
		self.tokenizer = tokenizer
		self.max_length = max_length
		self.thought_processor = ThoughtProcessor()

		# Load generated data
		self.examples = self._load_data(data_file)

		print(f"Loaded {len(self.examples)} parent-child training examples")

	def _load_data(self, data_file: str) -> List[Dict[str, Any]]:
		"""Load and preprocess generated training data"""
		examples = []

		with open(data_file, 'r') as f:
			for line in f:
				try:
					data = json.loads(line.strip())

					# Process thoughts
					if data.get("thoughts"):
						thought_structure = self.thought_processor.extract_reasoning_structure(
							data["thoughts"]
						)
						data["thought_structure"] = thought_structure

					examples.append(data)

				except json.JSONDecodeError:
					continue

		return examples

	def __len__(self) -> int:
		return len(self.examples)

	def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
		"""Get formatted training example"""
		example = self.examples[idx]

		# Create input text combining prompt and thoughts
		prompt = example["prompt"]
		thoughts = example.get("thoughts", "")
		output = example.get("output", "")

		# Format for training: prompt -> thoughts -> output
		if thoughts:
			full_text = f"Human: {prompt}\n\nThoughts: {thoughts}\n\nAssistant: {output}"
		else:
			full_text = f"Human: {prompt}\n\nAssistant: {output}"

		# Tokenize
		encoding = self.tokenizer(
			full_text,
			max_length=self.max_length,
			padding="max_length",
			truncation=True,
			return_tensors="pt"
		)

		input_ids = encoding["input_ids"].squeeze(0)
		attention_mask = encoding["attention_mask"].squeeze(0)

		# Create labels (compute loss only on assistant response)
		labels = input_ids.clone()

		# Find assistant response start
		assistant_token = "Assistant:"
		assistant_ids = self.tokenizer.encode(assistant_token, add_special_tokens=False)

		# Mask everything before assistant response
		for i in range(len(input_ids) - len(assistant_ids)):
			if torch.equal(input_ids[i:i+len(assistant_ids)], torch.tensor(assistant_ids)):
				labels[:i+len(assistant_ids)] = -100
				break

		result = {
			"input_ids": input_ids,
			"attention_mask": attention_mask,
			"labels": labels,
			"task_type": example.get("task_type", "general"),
		}

		# Add thought structure if available
		if "thought_structure" in example:
			# Convert thought structure to tensors for distillation
			structure = example["thought_structure"]
			result["complexity_score"] = torch.tensor(structure["complexity_score"], dtype=torch.float32)
			result["reasoning_pattern_count"] = torch.tensor(
				sum(structure["reasoning_patterns"].values()), dtype=torch.float32
			)

		return result

class ParentChildTrainer(DiffusionTrainer):
	"""
	Enhanced trainer that incorporates parent model guidance.

	This trainer extends the base DiffusionTrainer with:
	1. Knowledge distillation capabilities
	2. Dynamic data generation from parent model
	3. Thought-guided training objectives
	4. Curriculum learning with parent guidance
	"""

	def __init__(self, config: ParentChildTrainingConfig):
		# Initialize parent components
		self.parent_child_config = config
		self.data_generator = DataGenerator(
			config.parent_config,
			config.data_generation_config
		)

		# Initialize base trainer with child config
		super().__init__(config.child_training_config)

		# Override loss function with knowledge distillation
		self.loss_fn = KnowledgeDistillationLoss(
			alpha=config.distillation_alpha,
			beta=config.distillation_beta,
			gamma=config.distillation_gamma,
			temperature=config.distillation_temperature
		)

		# Data refresh tracking
		self.last_data_generation = 0

	async def initialize_training_data(self) -> None:
		"""Generate initial training data from parent model"""
		self.logger.info("Generating initial training data from parent model...")

		training_data = await self.data_generator.generate_training_data()

		# Create training dataset
		data_file = Path(self.parent_child_config.data_generation_config.output_dir) / "training_data.jsonl"

		self.train_dataset = ParentChildDataset(
			str(data_file),
			self.train_dataset.tokenizer,
			self.config.max_length
		)

		# Recreate data loader
		self.train_loader = DataLoader(
			self.train_dataset,
			batch_size=self.config.batch_size,
			shuffle=True,
			num_workers=self.config.dataloader_num_workers,
			pin_memory=True,
			drop_last=True
		)

		self.logger.info(f"Training data initialized with {len(self.train_dataset)} examples")

	def train_step(self, batch: Dict[str, torch.Tensor]) -> Dict[str, float]:
		"""Enhanced training step with knowledge distillation"""
		self.model.train()

		# Validate inputs
		if not validate_inputs(batch):
			return {"total": 0.0, "error": 1.0}

		# Move batch to device
		batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}

		# Determine training phase
		use_distillation = (
			self.global_step >= self.parent_child_config.distillation_start_step and
			self.global_step >= self.parent_child_config.warmup_steps
		)

		# Get diffusion steps from curriculum
		diffusion_steps = self.curriculum.get_diffusion_steps(self.global_step, self.config.max_steps)

		try:
			# Forward pass through student model
			with torch.cuda.amp.autocast(enabled=self.config.fp16 or self.config.bf16):
				outputs = self.model(
					input_ids=batch["input_ids"],
					attention_mask=batch["attention_mask"],
					max_steps=diffusion_steps,
					use_diffusion=True,
					return_all_steps=True
				)

				# Prepare teacher data for distillation
				teacher_data = {}
				if use_distillation:
					teacher_data = self._prepare_teacher_data(batch, outputs)

				# Compute losses
				if use_distillation and teacher_data:
					losses = self.loss_fn(outputs, teacher_data, batch)
				else:
					# Use standard diffusion loss during warmup
					from train import DiffusionLoss
					standard_loss_fn = DiffusionLoss(self.config)
					step_outputs = outputs.get("all_states", [outputs])
					losses = standard_loss_fn(outputs, batch, step_outputs, diffusion_steps)

			# Backward pass
			scaled_loss = self.scaler.scale(losses["total"])
			scaled_loss.backward()

			# Update training metrics
			self._update_training_metrics(batch, outputs)

		except Exception as e:
			self.logger.error(f"Training step failed: {e}")
			return {"total": float('inf'), "error": 1.0}

		# Gradient accumulation and optimization
		if (self.global_step + 1) % self.config.gradient_accumulation_steps == 0:
			# Gradient clipping
			self.scaler.unscale_(self.optimizer)
			grad_norm = torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.max_grad_norm)

			# Optimizer step
			self.scaler.step(self.optimizer)
			self.scaler.update()

			# Learning rate scheduling
			if self.global_step < self.config.warmup_steps:
				self.warmup_scheduler.step()
			else:
				self.scheduler.step()

			self.optimizer.zero_grad()

			losses["grad_norm"] = grad_norm.item() if isinstance(grad_norm, torch.Tensor) else grad_norm

		return {k: v.item() if torch.is_tensor(v) else v for k, v in losses.items()}

	def _prepare_teacher_data(self, batch: Dict[str, torch.Tensor],
							student_outputs: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
		"""Prepare teacher data for knowledge distillation"""
		teacher_data = {}

		# For now, we'll use simplified teacher data
		# In a full implementation, you might want to:
		# 1. Store teacher logits during data generation
		# 2. Encode thoughts into embeddings
		# 3. Compute teacher confidence scores

		# Placeholder implementation
		if "complexity_score" in batch:
			# Use complexity score as a proxy for teacher confidence
			teacher_confidence = torch.sigmoid(batch["complexity_score"]).unsqueeze(0)
			if teacher_confidence.dim() == 1:
				teacher_confidence = teacher_confidence.unsqueeze(0)

			# Expand to match sequence length
			seq_len = student_outputs["logits"].size(1)
			teacher_confidence = teacher_confidence.expand(-1, seq_len)
			teacher_data["teacher_confidence"] = teacher_confidence

		return teacher_data

	async def refresh_training_data(self) -> None:
		"""Refresh training data by generating new examples from parent model"""
		if (self.global_step - self.last_data_generation <
			self.parent_child_config.regenerate_data_interval):
			return

		self.logger.info("Refreshing training data with new examples from parent model...")

		# Generate new data
		new_data = await self.data_generator.generate_training_data()

		# Mix with existing data
		# (In practice, you'd implement more sophisticated data mixing)

		self.last_data_generation = self.global_step
		self.logger.info("Training data refreshed successfully")

	async def train(self) -> None:
		"""Enhanced training loop with parent-child setup"""
		# Initialize training data from parent model
		await self.initialize_training_data()

		# Start standard training loop
		self.logger.info("Starting Parent-Child Diffusion LLM training...")

		# Create output directory
		Path(self.config.output_dir).mkdir(parents=True, exist_ok=True)

		data_iter = iter(self.train_loader)
		moving_avg_loss = None
		best_checkpoint_step = 0

		for step in range(self.global_step, self.config.max_steps):
			self.global_step = step

			try:
				batch = next(data_iter)
			except StopIteration:
				data_iter = iter(self.train_loader)
				batch = next(data_iter)

			# Training step
			start_time = time.time()
			losses = self.train_step(batch)
			step_time = time.time() - start_time

			# Handle training errors
			if losses.get("error", 0) > 0:
				self.logger.warning(f"Training error at step {step}")
				continue

			# Moving average loss
			if moving_avg_loss is None:
				moving_avg_loss = losses["total"]
			else:
				moving_avg_loss = 0.9 * moving_avg_loss + 0.1 * losses["total"]

			# Periodic data refresh
			if step % 1000 == 0:  # Check every 1000 steps
				await self.refresh_training_data()

			# Logging
			if step % self.config.logging_steps == 0:
				lr = self.optimizer.param_groups[0]['lr']
				diffusion_steps = self.curriculum.get_diffusion_steps(step, self.config.max_steps)

				use_distillation = step >= self.parent_child_config.distillation_start_step

				log_dict = {
					"step": step,
					"learning_rate": lr,
					"diffusion_steps": diffusion_steps,
					"moving_avg_loss": moving_avg_loss,
					"step_time": step_time,
					"use_distillation": use_distillation,
					**losses
				}

				self.logger.info(
					f"Step {step}: Loss = {moving_avg_loss:.4f}, "
					f"LR = {lr:.2e}, Distillation = {use_distillation}, "
					f"Time = {step_time:.2f}s"
				)

				if self.config.wandb_project and self.config.local_rank <= 0:
					wandb.log(log_dict, step=step)

			# Evaluation and checkpointing (same as base trainer)
			if step > 0 and step % self.config.eval_steps == 0:
				eval_start_time = time.time()
				val_losses = self.evaluate()
				eval_time = time.time() - eval_start_time

				if val_losses and self.config.local_rank <= 0:
					self.logger.info(f"Validation - Step {step}: {val_losses}")

					if self.config.wandb_project:
						wandb.log(val_losses, step=step)

					val_loss = val_losses.get("val_total", float('inf'))
					if val_loss < self.best_val_loss:
						self.best_val_loss = val_loss
						best_checkpoint_step = step
						save_checkpoint(
							self.model, self.optimizer, self.scheduler,
							step, self.config, val_losses, self.train_dataset.tokenizer
						)

			# Regular checkpointing
			if step > 0 and step % self.config.save_steps == 0:
				save_checkpoint(
					self.model, self.optimizer, self.scheduler,
					step, self.config, losses, self.train_dataset.tokenizer
				)

		self.logger.info(f"Training completed! Best checkpoint at step {best_checkpoint_step}")

# ============================================================================
# MAIN TRAINING SCRIPT
# ============================================================================

async def main():
	"""Main function to run parent-child training"""

	# Configure comprehensive training setup
	config = ParentChildTrainingConfig(
		# Parent model configuration
		parent_config=ParentModelConfig(
			model_name="DeepSeek-R1-0528-Qwen3-8B-BF16:latest",
			ollama_host="http://localhost:11434",
			max_tokens=2048,
			temperature=0.7,
			concurrent_requests=4
		),

		# Data generation configuration
		data_generation_config=DataGenerationConfig(
			num_examples_per_category=2000,
			max_concurrent_generations=4,
			output_dir="./generated_training_data",
			reasoning_weight=0.3,
			factual_weight=0.2,
			creative_weight=0.2,
			instruction_weight=0.2,
			conversation_weight=0.1
		),

		# Child model training configuration
		child_training_config=TrainingConfig(
			model_config=DiffusionConfig(
				vocab_size=50257,
				hidden_dim=768,
				num_layers=12,
				num_heads=12,
				max_length=1024,
				diffusion_steps=14,
				use_kv_cache=True,
				confidence_calibration=True
			),
			learning_rate=3e-5,
			batch_size=6,
			max_steps=50000,
			warmup_steps=1000,
			gradient_accumulation_steps=4,
			eval_steps=1000,
			save_steps=2500,
			logging_steps=50,
			output_dir="./parent_child_checkpoints",
			wandb_project="parent-child-diffusion-llm",
			run_name="deepseek-r1-distillation-v1",
			bf16=True
		),

		# Knowledge distillation parameters
		distillation_alpha=0.6,    # Generation loss weight
		distillation_beta=0.25,    # Thought alignment weight
		distillation_gamma=0.15,   # Output alignment weight
		distillation_temperature=3.0,

		# Training curriculum
		use_curriculum=True,
		warmup_steps=1000,
		distillation_start_step=2000,
		regenerate_data_interval=10000
	)

	# Setup logging
	logging.basicConfig(
		level=logging.INFO,
		format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
		handlers=[
			logging.FileHandler('parent_child_training.log'),
			logging.StreamHandler()
		]
	)

	logger = logging.getLogger(__name__)
	logger.info("Starting Parent-Child Diffusion LLM Training System")

	try:
		# Initialize trainer
		trainer = ParentChildTrainer(config)

		# Start training
		await trainer.train()

		logger.info("Training completed successfully!")

	except Exception as e:
		logger.error(f"Training failed with error: {str(e)}")
		raise

if __name__ == "__main__":
	# Run the async training
	asyncio.run(main())
