
from collections import defaultdict, deque
from concurrent.futures import <PERSON><PERSON>oolExecutor
from dataclasses import dataclass, field
from pathlib import Path
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.optim import AdamW
from torch.optim.lr_scheduler import CosineAnnealingLR, LinearLR
from torch.utils.data import Dataset, DataLoader, DistributedSampler
from tqdm import tqdm
from transformers import AutoTokenizer
from typing import Dict, List, Optional, Tuple, Union, Any
from torch.utils.checkpoint import checkpoint as tutils_checkpoint

import json
import logging
import math
import numpy as np
import orjson
import random
import time
import torch
import torch.distributed as dist
import torch.nn as nn
import torch.nn.functional as F
import wandb
import warnings
import multiprocessing as mp

# MEMORY PROFILING IMPORTS
import torch.profiler
import psutil
import gc

# Import our Diffusion LLM components - Updated to match actual model.py
from model import (
	DiffusionLLM, DiffusionConfig, DiffusionScheduler,
	MemoryManager, ConfidenceEstimator, TokenEditor,
	validate_tensor_shape, clip_gradients
)

def _validate_chunk(chunk_data: tuple) -> List[Dict]:
	"""Validate a chunk of examples, pickleable for multiprocessing."""
	examples, formatter, format_name = chunk_data
	return [example for example in examples if formatter.validate_example(example, format_name)]

def _parse_chunk(lines: List[bytes], file_path: str) -> List[Dict]:
	"""Parse a chunk of byte lines into a list of dictionaries."""
	data = []
	for line in lines:
		if line:  # Skip empty lines
			try:
				data.append(orjson.loads(line))
			except orjson.JSONDecodeError as e:
				warnings.warn(f"Invalid JSON in {file_path}: {e}")
	return data

def _process_chunk(chunk_data: tuple) -> List[Dict]:
	"""Wrapper function to process a chunk, pickleable for multiprocessing."""
	lines, file_path = chunk_data
	return _parse_chunk(lines, file_path)

@dataclass
class TrainingConfig:
	"""Comprehensive training configuration"""
	# Model config
	model_config: DiffusionConfig = field(default_factory=DiffusionConfig)

	# Training hyperparameters
	learning_rate: float = 5e-5
	weight_decay: float = 0.01
	warmup_steps: int = 1000
	max_steps: int = 100000
	gradient_accumulation_steps: int = 4
	max_grad_norm: float = 1.0

	# Diffusion-specific
	diffusion_curriculum: bool = True
	start_diffusion_steps: int = 3
	end_diffusion_steps: int = 14
	curriculum_schedule: str = "linear"  # "linear", "exponential", "cosine"

	# Data config
	batch_size: int = 2
	max_length: int = 1024
	tokenizer_name: str = "gpt2"

	# Training format weights
	pretraining_weight: float = 0.4
	instruction_weight: float = 0.3
	conversation_weight: float = 0.2
	agentic_weight: float = 0.1

	# Loss weights
	generation_loss_weight: float = 1.0
	consistency_loss_weight: float = 0.1
	memory_loss_weight: float = 0.05
	confidence_loss_weight: float = 0.05

	# Evaluation
	eval_steps: int = 1000
	save_steps: int = 5000
	logging_steps: int = 100

	# Paths
	output_dir: str = "./checkpoints"
	data_dir: str = "./data"
	cache_dir: str = "./cache"
	resume_from_checkpoint: Optional[str] = None

	# Distributed training
	local_rank: int = -1
	world_size: int = 1

	# Logging
	wandb_project: Optional[str] = None
	run_name: Optional[str] = None

	# Mixed precision
	fp16: bool = False
	bf16: bool = True

	# Advanced training options
	use_gradient_checkpointing: bool = True
	dataloader_num_workers: int = 4
	save_total_limit: int = 5

	# NEW: Data augmentation options
	use_data_augmentation: bool = True
	corruption_probability: float = 0.3
	max_corruption_ratio: float = 0.2
	max_length: int = 1024
	pad_token_id : int = 0

	# NEW: Dynamic batching options
	use_dynamic_batching: bool = False
	min_batch_size: int = 2
	max_batch_size: int = 4

	# NEW: Advanced evaluation options
	compute_generation_metrics: bool = True
	compute_calibration_metrics: bool = True
	max_generation_samples: int = 100

	def __post_init__(self):
		"""Validate training configuration"""
		assert self.batch_size > 0, "Batch size must be positive"
		assert self.max_steps > 0, "Max steps must be positive"
		assert 0 <= self.start_diffusion_steps <= self.end_diffusion_steps, "Invalid diffusion step range"
		total_weight = sum([self.pretraining_weight, self.instruction_weight,
						self.conversation_weight, self.agentic_weight])
		assert abs(total_weight - 1.0) < 1e-6, f"Format weights must sum to 1.0, got {total_weight}"

# ============================================================================
# NEW: DATA AUGMENTATION FOR DIFFUSION TRAINING
# ============================================================================

class DiffusionDataAugmentation:
	"""Data augmentation strategies for diffusion training"""
	def __init__(self, tokenizer, config: TrainingConfig):
		self.tokenizer = tokenizer
		self.config = config
		self.vocab_size = tokenizer.vocab_size
		self.max_length = config.max_length
		self.pad_token_id = config.pad_token_id

	def create_progressive_corruption(self, input_ids: torch.Tensor,
									 corruption_step: int,
									 max_steps: int) -> Tuple[torch.Tensor, torch.Tensor]:
		"""Create progressively corrupted versions and corresponding attention mask"""
		batch_size, seq_len = input_ids.shape
		device = input_ids.device

		# Validate input sequence length
		if seq_len > self.max_length:
			raise ValueError(f"Input sequence length {seq_len} exceeds max_length {self.max_length}")

		# Pad input_ids to max_length if necessary
		if seq_len < self.max_length:
			padding = torch.full(
				(batch_size, self.max_length - seq_len),
				self.pad_token_id,
				device=device,
				dtype=input_ids.dtype
			)
			input_ids = torch.cat([input_ids, padding], dim=1)
			seq_len = self.max_length

		# Calculate corruption ratio based on step
		corruption_ratio = (max_steps - corruption_step) / max_steps
		corruption_ratio *= self.config.max_corruption_ratio

		if corruption_ratio <= 0:
			attention_mask = torch.ones(batch_size, seq_len, device=device, dtype=torch.float)
			return input_ids.clone(), attention_mask

		corrupted_ids = input_ids.clone()
		attention_mask = torch.ones(batch_size, seq_len, device=device, dtype=torch.float)

		# Define valid positions (skip first 2 and last 2 tokens)
		valid_positions = torch.arange(2, seq_len - 2, device=device)
		num_valid = len(valid_positions)

		if num_valid == 0:
			return corrupted_ids, attention_mask

		# Calculate number of positions to corrupt per sequence
		num_corrupt = min(int(num_valid * corruption_ratio), num_valid)
		if num_corrupt == 0:
			return corrupted_ids, attention_mask

		# Randomly select positions to corrupt (same for all sequences in batch)
		indices = torch.randperm(num_valid, device=device)[:num_corrupt]
		corrupt_positions = valid_positions[indices]  # Shape: (num_corrupt,)

		# Expand corrupt_positions to match batch dimension
		corrupt_positions = corrupt_positions.unsqueeze(0).expand(batch_size, num_corrupt)  # Shape: (batch_size, num_corrupt)

		# Create batch indices
		batch_indices = torch.arange(batch_size, device=device).unsqueeze(1).expand(-1, num_corrupt)  # Shape: (batch_size, num_corrupt)

		# Randomly assign corruption types
		corruption_types = torch.randint(0, 4, (batch_size, num_corrupt), device=device)  # 0: mask, 1: random, 2: repeat, 3: delete

		# Create masks for each corruption type
		mask_mask = corruption_types == 0
		random_mask = corruption_types == 1
		repeat_mask = corruption_types == 2
		delete_mask = corruption_types == 3

		# Apply mask corruption
		if mask_mask.any():
			mask_indices = torch.where(mask_mask)
			corrupted_ids[batch_indices[mask_indices], corrupt_positions[mask_indices]] = (
				self.tokenizer.unk_token_id or self.pad_token_id
			)

		# Apply random corruption
		if random_mask.any():
			random_indices = torch.where(random_mask)
			random_tokens = torch.randint(0, self.vocab_size, (random_mask.sum(),), device=device)
			corrupted_ids[batch_indices[random_indices], corrupt_positions[random_indices]] = random_tokens

		# Apply repeat corruption
		if repeat_mask.any():
			repeat_indices = torch.where(repeat_mask)
			valid_repeat_positions = corrupt_positions[repeat_indices].clamp(min=1)
			corrupted_ids[batch_indices[repeat_indices], corrupt_positions[repeat_indices]] = (
				corrupted_ids[batch_indices[repeat_indices], valid_repeat_positions - 1]
			)

		# Apply delete corruption (vectorized where possible)
		if delete_mask.any():
			delete_indices = torch.where(delete_mask)
			delete_batch_indices = batch_indices[delete_indices]
			delete_positions = corrupt_positions[delete_indices]

			# Sort deletions by position (descending) to avoid overwriting issues
			sorted_indices = torch.argsort(delete_positions, descending=True)
			delete_batch_indices = delete_batch_indices[sorted_indices]
			delete_positions = delete_positions[sorted_indices]

			# Track new sequence lengths after deletions
			new_lengths = torch.full((batch_size,), seq_len, device=device, dtype=torch.long)

			# Apply deletions and update lengths
			for b, pos in zip(delete_batch_indices, delete_positions):
				if pos < new_lengths[b] - 1:
					corrupted_ids[b, pos:new_lengths[b]-1] = corrupted_ids[b, pos+1:new_lengths[b]].clone()
					corrupted_ids[b, new_lengths[b]-1] = self.pad_token_id
					attention_mask[b, new_lengths[b]-1] = 0
					new_lengths[b] -= 1

			# Pad sequences to max_length
			for b in range(batch_size):
				if new_lengths[b] < self.max_length:
					corrupted_ids[b, new_lengths[b]:self.max_length] = self.pad_token_id
					attention_mask[b, new_lengths[b]:self.max_length] = 0

		return corrupted_ids, attention_mask

	def augment_batch(self, batch: Dict[str, torch.Tensor],
					 diffusion_steps: int) -> List[Dict[str, torch.Tensor]]:
		"""Create augmented training targets for multi-step diffusion"""
		if not self.config.use_data_augmentation:
			return [batch]

		augmented_batches = [batch]  # Original batch

		# Create intermediate corruption targets
		for step in range(1, diffusion_steps):
			corrupted_ids, attention_mask = self.create_progressive_corruption(
				batch["input_ids"], step, diffusion_steps
			)

			augmented_batch = {
				"input_ids": corrupted_ids,
				"attention_mask": attention_mask,
				"labels": batch["labels"].detach().clone(),
				"format_type": batch["format_type"],
				"corruption_step": step,
				"target_clean": batch["input_ids"].clone()
			}
			augmented_batches.append(augmented_batch)

		return augmented_batches

# ============================================================================
# NEW: ADVANCED EVALUATION METRICS
# ============================================================================

class AdvancedEvaluationMetrics:
	"""Comprehensive evaluation metrics for diffusion models"""

	def __init__(self, tokenizer):
		self.tokenizer = tokenizer

	def compute_bleu_score(self, predictions: List[str], references: List[str]) -> float:
		"""Compute BLEU score (simplified version)"""
		try:
			# Simplified BLEU implementation
			total_score = 0.0
			for pred, ref in zip(predictions, references):
				pred_tokens = pred.split()
				ref_tokens = ref.split()

				if len(pred_tokens) == 0:
					continue

				# 1-gram precision
				pred_1grams = set(pred_tokens)
				ref_1grams = set(ref_tokens)
				precision_1 = len(pred_1grams.intersection(ref_1grams)) / len(pred_1grams) if pred_1grams else 0

				# Brevity penalty
				bp = min(1.0, len(pred_tokens) / max(len(ref_tokens), 1))

				total_score += bp * precision_1

			return total_score / max(len(predictions), 1)
		except Exception:
			return 0.0

	def compute_rouge_score(self, predictions: List[str], references: List[str]) -> Dict[str, float]:
		"""Compute ROUGE scores (simplified version)"""
		try:
			rouge_1_scores = []
			rouge_l_scores = []

			for pred, ref in zip(predictions, references):
				pred_tokens = set(pred.split())
				ref_tokens = set(ref.split())

				# ROUGE-1: unigram overlap
				if len(ref_tokens) > 0:
					rouge_1 = len(pred_tokens.intersection(ref_tokens)) / len(ref_tokens)
					rouge_1_scores.append(rouge_1)

				# ROUGE-L: longest common subsequence (simplified)
				pred_words = pred.split()
				ref_words = ref.split()
				lcs_length = self._longest_common_subsequence(pred_words, ref_words)
				rouge_l = lcs_length / max(len(ref_words), 1)
				rouge_l_scores.append(rouge_l)

			return {
				"rouge_1": sum(rouge_1_scores) / max(len(rouge_1_scores), 1),
				"rouge_l": sum(rouge_l_scores) / max(len(rouge_l_scores), 1)
			}
		except Exception:
			return {"rouge_1": 0.0, "rouge_l": 0.0}

	def _longest_common_subsequence(self, seq1: List[str], seq2: List[str]) -> int:
		"""Compute LCS length for ROUGE-L"""
		m, n = len(seq1), len(seq2)
		dp = [[0] * (n + 1) for _ in range(m + 1)]

		for i in range(1, m + 1):
			for j in range(1, n + 1):
				if seq1[i-1] == seq2[j-1]:
					dp[i][j] = dp[i-1][j-1] + 1
				else:
					dp[i][j] = max(dp[i-1][j], dp[i][j-1])

		return dp[m][n]

	def compute_expected_calibration_error(self, confidences: torch.Tensor,
										 accuracies: torch.Tensor,
										 num_bins: int = 10) -> float:
		"""Compute Expected Calibration Error (ECE)"""
		try:
			confidences = confidences.flatten().cpu().numpy()
			accuracies = accuracies.flatten().cpu().numpy()

			bin_boundaries = np.linspace(0, 1, num_bins + 1)
			bin_lowers = bin_boundaries[:-1]
			bin_uppers = bin_boundaries[1:]

			ece = 0.0
			for bin_lower, bin_upper in zip(bin_lowers, bin_uppers):
				in_bin = (confidences > bin_lower) & (confidences <= bin_upper)
				prop_in_bin = in_bin.float().mean()  # Convert boolean to float before mean()

				if prop_in_bin > 0:
					accuracy_in_bin = accuracies[in_bin].float().mean()  # Ensure float type
					avg_confidence_in_bin = confidences[in_bin].mean()
					ece += np.abs(avg_confidence_in_bin - accuracy_in_bin) * prop_in_bin

			return float(ece)
		except Exception:
			return 0.0

	def compute_generation_quality_metrics(self, generated_texts: List[str],
										 reference_texts: List[str]) -> Dict[str, float]:
		"""Compute comprehensive generation quality metrics"""
		metrics = {}

		# Length statistics
		gen_lengths = [len(text.split()) for text in generated_texts]
		ref_lengths = [len(text.split()) for text in reference_texts]

		metrics["avg_generation_length"] = np.mean(gen_lengths)
		metrics["avg_reference_length"] = np.mean(ref_lengths)
		metrics["length_ratio"] = np.mean(gen_lengths) / max(np.mean(ref_lengths), 1)

		# BLEU and ROUGE scores
		metrics["bleu"] = self.compute_bleu_score(generated_texts, reference_texts)
		rouge_scores = self.compute_rouge_score(generated_texts, reference_texts)
		metrics.update(rouge_scores)

		# Diversity metrics
		unique_bigrams = set()
		total_bigrams = 0
		for text in generated_texts:
			words = text.split()
			for i in range(len(words) - 1):
				bigram = (words[i], words[i+1])
				unique_bigrams.add(bigram)
				total_bigrams += 1

		metrics["bigram_diversity"] = len(unique_bigrams) / max(total_bigrams, 1)

		return metrics

# ============================================================================
# NEW: DYNAMIC BATCHING SYSTEM
# ============================================================================

class DynamicBatcher:
	"""Dynamic batching based on sequence lengths and diffusion complexity"""

	def __init__(self, config: TrainingConfig):
		self.config = config
		self.min_batch_size = config.min_batch_size
		self.max_batch_size = config.max_batch_size

	def create_dynamic_batches(self, dataset: Dataset,
							  current_step: int) -> List[List[int]]:
		"""Create dynamic batches based on sequence lengths"""
		if not self.config.use_dynamic_batching:
			# Standard fixed batching
			batches = []
			for i in range(0, len(dataset), self.config.batch_size):
				batch_indices = list(range(i, min(i + self.config.batch_size, len(dataset))))
				batches.append(batch_indices)
			return batches

		# Get sequence lengths for all examples
		lengths = []
		for i in range(len(dataset)):
			try:
				example = dataset[i]
				seq_len = (example["attention_mask"] == 1).sum().item()
				lengths.append((i, seq_len))
			except:
				lengths.append((i, self.config.max_length))

		# Sort by length for efficient batching
		lengths.sort(key=lambda x: x[1])

		# Create batches with similar lengths
		batches = []
		current_batch = []
		current_length = 0

		for idx, length in lengths:
			# Adaptive batch size based on sequence length
			if length > 512:
				target_batch_size = self.min_batch_size
			elif length > 256:
				target_batch_size = (self.min_batch_size + self.max_batch_size) // 2
			else:
				target_batch_size = self.max_batch_size

			# Add to current batch if compatible
			if (len(current_batch) < target_batch_size and
				(current_length == 0 or abs(length - current_length) < 100)):
				current_batch.append(idx)
				current_length = length
			else:
				# Start new batch
				if current_batch:
					batches.append(current_batch)
				current_batch = [idx]
				current_length = length

		# Add final batch
		if current_batch:
			batches.append(current_batch)

		return batches

# ============================================================================
# NEW: TRAINING DYNAMICS ANALYZER
# ============================================================================

class TrainingDynamicsAnalyzer:
	"""Analyze and track training dynamics for diffusion models"""

	def __init__(self, config: TrainingConfig):
		self.config = config
		self.step_history = []
		self.quality_progression = defaultdict(list)
		self.edit_statistics = defaultdict(list)
		self.memory_usage_history = []
		self.consistency_scores = []

	def track_diffusion_step(self, step: int, state_info: Dict[str, Any]) -> None:
		"""Track information for a single diffusion step"""
		confidence_scores = state_info.get("confidence_scores", torch.tensor([0.5]))
		# Ensure confidence scores are float type for statistical operations
		if confidence_scores.dtype in [torch.long, torch.int, torch.int32, torch.int64]:
			confidence_scores = confidence_scores.float()

		self.step_history.append({
			"global_step": state_info.get("global_step", 0),
			"diffusion_step": step,
			"quality_score": state_info.get("quality_score", 0.0),
			"confidence_mean": confidence_scores.mean().item(),
			"confidence_std": confidence_scores.std().item(),
			"num_edits": state_info.get("num_edits", 0),
			"edit_acceptance_rate": state_info.get("edit_acceptance_rate", 1.0),
			"memory_utilization": state_info.get("memory_utilization", 0.0)
		})

	def track_edit_statistics(self, edit_info: Dict[str, Any]) -> None:
		"""Track token editing statistics"""
		self.edit_statistics["positions"].append(edit_info.get("positions", []))
		self.edit_statistics["old_tokens"].append(edit_info.get("old_tokens", []))
		self.edit_statistics["new_tokens"].append(edit_info.get("new_tokens", []))
		self.edit_statistics["confidence_scores"].append(edit_info.get("confidence_scores", []))
		self.edit_statistics["phase"].append(edit_info.get("phase", "unknown"))

	def analyze_convergence_patterns(self) -> Dict[str, Any]:
		"""Analyze convergence patterns across training"""
		if not self.step_history:
			return {}

		analysis = {}

		# Quality progression analysis
		quality_scores = [step["quality_score"] for step in self.step_history]
		if quality_scores:
			analysis["quality_improvement_rate"] = np.polyfit(
				range(len(quality_scores)), quality_scores, 1
			)[0] if len(quality_scores) > 1 else 0.0

			analysis["quality_variance"] = np.var(quality_scores)
			analysis["quality_trend"] = "improving" if analysis["quality_improvement_rate"] > 0 else "declining"

		# Confidence analysis
		confidence_means = [step["confidence_mean"] for step in self.step_history]
		confidence_stds = [step["confidence_std"] for step in self.step_history]

		if confidence_means:
			analysis["confidence_stability"] = 1.0 - np.std(confidence_means)
			analysis["confidence_calibration_trend"] = np.mean(confidence_stds)

		# Edit acceptance analysis
		edit_rates = [step["edit_acceptance_rate"] for step in self.step_history]
		if edit_rates:
			analysis["avg_edit_acceptance"] = np.mean(edit_rates)
			analysis["edit_stability"] = 1.0 - np.std(edit_rates)

		return analysis

	def generate_training_report(self) -> Dict[str, Any]:
		"""Generate comprehensive training analysis report"""
		report = {
			"total_steps_tracked": len(self.step_history),
			"convergence_analysis": self.analyze_convergence_patterns(),
			"edit_statistics": self._analyze_edit_patterns(),
			"memory_analysis": self._analyze_memory_usage(),
		}

		return report

	def _analyze_edit_patterns(self) -> Dict[str, Any]:
		"""Analyze token editing patterns"""
		if not self.edit_statistics["positions"]:
			return {}

		# Flatten edit positions
		all_positions = []
		for positions in self.edit_statistics["positions"]:
			if torch.is_tensor(positions):
				all_positions.extend(positions.flatten().tolist())
			else:
				all_positions.extend(positions)

		# Analyze phase distribution
		phase_counts = {}
		for phase in self.edit_statistics["phase"]:
			phase_counts[phase] = phase_counts.get(phase, 0) + 1

		return {
			"total_edits": len(all_positions),
			"avg_edits_per_step": len(all_positions) / max(len(self.edit_statistics["positions"]), 1),
			"phase_distribution": phase_counts,
			"edit_position_variance": np.var(all_positions) if all_positions else 0.0
		}

	def _analyze_memory_usage(self) -> Dict[str, Any]:
		"""Analyze memory system usage patterns"""
		if not self.memory_usage_history:
			return {}

		return {
			"avg_memory_utilization": np.mean(self.memory_usage_history),
			"memory_utilization_trend": np.polyfit(
				range(len(self.memory_usage_history)),
				self.memory_usage_history, 1
			)[0] if len(self.memory_usage_history) > 1 else 0.0
		}

# ============================================================================
# ENHANCED TEXT FORMAT PROCESSORS
# ============================================================================

class TextFormatter:
	"""Base class for text format processing with validation"""

	def __init__(self, tokenizer, max_length: int = 1024):
		self.tokenizer = tokenizer
		self.max_length = max_length

	def format_example(self, example: Dict[str, Any]) -> Dict[str, torch.Tensor]:
		"""Format a single example into model inputs"""
		raise NotImplementedError

	def validate_example(self, example: Dict[str, Any], format_type: str) -> bool:
		"""Validate example has required fields"""
		try:
			formatted = self.format_example(example)
			return all(key in formatted for key in ["input_ids", "attention_mask", "labels"])
		except Exception as e:
			warnings.warn(f"Invalid {format_type} example: {str(e)}")
			return False

class PretrainingFormatter(TextFormatter):
	"""Format raw text for pretraining with validation"""

	def format_example(self, example: Dict[str, Any]) -> Dict[str, torch.Tensor]:
		text = example.get("text", "")

		if not text or not text.strip():
			raise ValueError("Empty text field")

		# Tokenize with padding/truncation
		encoding = self.tokenizer(
			text,
			max_length=self.max_length,
			padding="max_length",
			truncation=True,
			return_tensors="pt"
		)

		input_ids = encoding["input_ids"].squeeze(0)
		attention_mask = encoding["attention_mask"].squeeze(0)

		return {
			"input_ids": input_ids,
			"attention_mask": attention_mask,
			"labels": input_ids.clone(),  # Autoregressive target
			"format_type": "pretraining"
		}

class InstructionFormatter(TextFormatter):
	"""Format instruction-following datasets with validation"""

	def __init__(self, tokenizer, max_length: int = 1024):
		super().__init__(tokenizer, max_length)
		self.instruction_template = "### Instruction:\n{instruction}\n\n### Response:\n{response}"

	def format_example(self, example: Dict[str, Any]) -> Dict[str, torch.Tensor]:
		instruction = example.get("instruction", "")
		response = example.get("output", example.get("response", ""))

		if not instruction or not response:
			raise ValueError("Missing instruction or response")

		# Add input context if available
		if "input" in example and example["input"].strip():
			instruction += f"\n\nInput: {example['input']}"

		formatted_text = self.instruction_template.format(
			instruction=instruction,
			response=response
		)

		# Tokenize full sequence
		encoding = self.tokenizer(
			formatted_text,
			max_length=self.max_length,
			padding="max_length",
			truncation=True,
			return_tensors="pt"
		)

		input_ids = encoding["input_ids"].squeeze(0)
		attention_mask = encoding["attention_mask"].squeeze(0)

		# Create labels that only compute loss on response tokens
		labels = input_ids.clone()

		# Find response start
		response_start_text = "### Response:\n"
		response_tokens = self.tokenizer.encode(response_start_text, add_special_tokens=False)

		# Mask instruction tokens (set to -100 to ignore in loss)
		response_start_idx = None
		for i in range(len(input_ids) - len(response_tokens)):
			if torch.equal(input_ids[i:i+len(response_tokens)], torch.tensor(response_tokens)):
				response_start_idx = i + len(response_tokens)
				break

		if response_start_idx is not None:
			labels[:response_start_idx] = -100

		return {
			"input_ids": input_ids,
			"attention_mask": attention_mask,
			"labels": labels,
			"format_type": "instruction"
		}

class ConversationFormatter(TextFormatter):
	"""Format multi-turn conversations with validation"""

	def __init__(self, tokenizer, max_length: int = 1024):
		super().__init__(tokenizer, max_length)
		self.system_token = "<|system|>"
		self.user_token = "<|user|>"
		self.assistant_token = "<|assistant|>"
		self.end_token = "<|end|>"

		# Add special tokens to tokenizer if not present
		special_tokens = [self.system_token, self.user_token, self.assistant_token, self.end_token]
		added_tokens = self.tokenizer.add_special_tokens({"additional_special_tokens": special_tokens})
		if added_tokens > 0:
			print(f"Added {added_tokens} special tokens for conversations")

	def format_example(self, example: Dict[str, Any]) -> Dict[str, torch.Tensor]:
		conversations = example.get("conversations", [])

		if not conversations:
			raise ValueError("Empty conversations")

		# Build conversation string
		formatted_parts = []

		if "system" in example:
			formatted_parts.append(f"{self.system_token}{example['system']}{self.end_token}")

		for turn in conversations:
			role = turn.get("from", turn.get("role", ""))
			content = turn.get("value", turn.get("content", ""))

			if not content:
				continue

			if role in ["human", "user"]:
				formatted_parts.append(f"{self.user_token}{content}{self.end_token}")
			elif role in ["gpt", "assistant"]:
				formatted_parts.append(f"{self.assistant_token}{content}{self.end_token}")

		if not formatted_parts:
			raise ValueError("No valid conversation turns")

		formatted_text = "".join(formatted_parts)

		encoding = self.tokenizer(
			formatted_text,
			max_length=self.max_length,
			padding="max_length",
			truncation=True,
			return_tensors="pt"
		)

		input_ids = encoding["input_ids"].squeeze(0)
		attention_mask = encoding["attention_mask"].squeeze(0)

		# Create labels that only compute loss on assistant responses
		labels = input_ids.clone()

		# Mask everything except assistant responses
		assistant_token_id = self.tokenizer.convert_tokens_to_ids(self.assistant_token)
		end_token_id = self.tokenizer.convert_tokens_to_ids(self.end_token)

		in_assistant_response = False
		for i, token_id in enumerate(input_ids):
			if token_id == assistant_token_id:
				in_assistant_response = True
				labels[i] = -100  # Don't compute loss on the assistant token itself
			elif token_id == end_token_id and in_assistant_response:
				in_assistant_response = False
				labels[i] = -100  # Don't compute loss on end token
			elif not in_assistant_response:
				labels[i] = -100

		return {
			"input_ids": input_ids,
			"attention_mask": attention_mask,
			"labels": labels,
			"format_type": "conversation"
		}

class AgenticFormatter(TextFormatter):
	"""Format agentic/tool-use datasets with validation"""

	def __init__(self, tokenizer, max_length: int = 1024):
		super().__init__(tokenizer, max_length)
		self.thought_token = "<|thought|>"
		self.action_token = "<|action|>"
		self.observation_token = "<|observation|>"
		self.end_token = "<|end|>"

		# Add special tokens
		special_tokens = [self.thought_token, self.action_token, self.observation_token, self.end_token]
		added_tokens = self.tokenizer.add_special_tokens({"additional_special_tokens": special_tokens})
		if added_tokens > 0:
			print(f"Added {added_tokens} special tokens for agentic tasks")

	def format_example(self, example: Dict[str, Any]) -> Dict[str, torch.Tensor]:
		# Handle different agentic formats
		if "steps" in example:
			# ReAct-style format
			formatted_parts = []

			task = example.get("task", example.get("question", ""))
			if task:
				formatted_parts.append(f"Task: {task}\n")

			for step in example["steps"]:
				if "thought" in step:
					formatted_parts.append(f"{self.thought_token}{step['thought']}{self.end_token}")
				if "action" in step:
					formatted_parts.append(f"{self.action_token}{step['action']}{self.end_token}")
				if "observation" in step:
					formatted_parts.append(f"{self.observation_token}{step['observation']}{self.end_token}")

			if "answer" in example:
				formatted_parts.append(f"Answer: {example['answer']}")

		else:
			# Simple tool use format
			task = example.get("task", "")
			actions = example.get("actions", [])

			if not task:
				raise ValueError("Missing task description")

			formatted_parts = [f"Task: {task}\n"]

			for action in actions:
				formatted_parts.append(f"{self.action_token}{action}{self.end_token}")

		if not formatted_parts:
			raise ValueError("No valid agentic content")

		formatted_text = "".join(formatted_parts)

		encoding = self.tokenizer(
			formatted_text,
			max_length=self.max_length,
			padding="max_length",
			truncation=True,
			return_tensors="pt"
		)

		input_ids = encoding["input_ids"].squeeze(0)
		attention_mask = encoding["attention_mask"].squeeze(0)

		# For agentic tasks, compute loss on thoughts and actions
		labels = input_ids.clone()

		# Mask observations (external feedback)
		obs_token_id = self.tokenizer.convert_tokens_to_ids(self.observation_token)
		end_token_id = self.tokenizer.convert_tokens_to_ids(self.end_token)

		in_observation = False
		for i, token_id in enumerate(input_ids):
			if token_id == obs_token_id:
				in_observation = True
				labels[i] = -100
			elif token_id == end_token_id and in_observation:
				in_observation = False
				labels[i] = -100
			elif in_observation:
				labels[i] = -100

		return {
			"input_ids": input_ids,
			"attention_mask": attention_mask,
			"labels": labels,
			"format_type": "agentic"
		}

# ============================================================================
# ENHANCED DATASET WITH AUGMENTATION
# ============================================================================

class MultiFormatDataset(Dataset):
	"""Dataset that handles multiple text formats with validation and augmentation"""

	def __init__(self, config: TrainingConfig, split: str = "train"):
		self.config = config
		self.split = split

		# Initialize tokenizer
		self.tokenizer = AutoTokenizer.from_pretrained(config.tokenizer_name)
		if self.tokenizer.pad_token is None:
			self.tokenizer.pad_token = self.tokenizer.eos_token

		# Initialize formatters and augmentation
		self.formatters = {
			"pretraining": PretrainingFormatter(self.tokenizer, config.max_length),
			"instruction": InstructionFormatter(self.tokenizer, config.max_length),
			"conversation": ConversationFormatter(self.tokenizer, config.max_length),
			"agentic": AgenticFormatter(self.tokenizer, config.max_length)
		}

		self.augmentation = DiffusionDataAugmentation(self.tokenizer, config)

		# Load and validate datasets
		self.datasets = self._load_datasets()
		self.format_weights = self._get_format_weights()

		# Create sampling weights
		self.examples = self._prepare_examples()

		print(f"Loaded {len(self.examples)} examples for {split} split")

	def _load_datasets(self) -> Dict[str, List[Dict]]:
		"""Load datasets for each format with parallel validation."""
		datasets = {}
		data_dir = Path(self.config.data_dir)

		format_files = {
			"pretraining": data_dir / f"pretraining_{self.split}.jsonl",
			"instruction": data_dir / f"instruction_{self.split}.jsonl",
			"conversation": data_dir / f"conversation_{self.split}.jsonl",
			"agentic": data_dir / f"agentic_{self.split}.jsonl"
		}

		for format_name, file_path in format_files.items():
			if file_path.exists():
				print(f"Loading {format_name} data from {file_path}")
				raw_data = self._load_jsonl(file_path)

				# Validate examples
				print(f"Validating {format_name} data...")
				threshold = 10000  # Adjust based on testing (e.g., 10K examples)

				if len(raw_data) < threshold:
					# Sequential validation for small datasets
					valid_data = [
						example for example in raw_data
						if self.formatters[format_name].validate_example(example, format_name)
					]
				else:
					# Parallel validation for large datasets
					num_cores = mp.cpu_count() - 2
					chunk_size = max(1, len(raw_data) // num_cores + 1)
					chunks = [
						(raw_data[i:i + chunk_size], self.formatters[format_name], format_name)
						for i in range(0, len(raw_data), chunk_size)
					]

					valid_data = []
					with ProcessPoolExecutor(max_workers=num_cores) as executor:
						results = executor.map(_validate_chunk, chunks)
						for result in results:
							valid_data.extend(result)

				print(f"Loaded {len(valid_data)}/{len(raw_data)} valid {format_name} examples")
				datasets[format_name] = valid_data
			else:
				print(f"File not found: {file_path}")

		return datasets

	def _load_jsonl(self, file_path: Path) -> List[Dict]:
		"""Fast JSONL loading by reading entire file into memory and processing in parallel."""
		print("Reading file...")
		with open(file_path, 'rb') as f:
			lines = f.read().split(b'\n')

		if not lines:
			print("Processing complete: empty file")
			return []

		print("Processing file...")
		# Split lines into chunks for parallel processing
		num_cores = mp.cpu_count()
		chunk_size = max(1, len(lines) // num_cores + 1)
		chunks = [(lines[i:i + chunk_size], str(file_path)) for i in range(0, len(lines), chunk_size)]

		# Parse chunks in parallel
		data = []
		with ProcessPoolExecutor(max_workers=num_cores-1) as executor:
			results = executor.map(_process_chunk, chunks)
			for result in results:
				data.extend(result)

		print("Processing complete")
		return data

	def _get_format_weights(self) -> Dict[str, float]:
		"""Get sampling weights for each format"""
		return {
			"pretraining": self.config.pretraining_weight,
			"instruction": self.config.instruction_weight,
			"conversation": self.config.conversation_weight,
			"agentic": self.config.agentic_weight
		}

	def _prepare_examples(self) -> List[Tuple[str, int]]:
		"""Prepare weighted list of (format, index) pairs"""
		examples = []

		for format_name, dataset in self.datasets.items():
			weight = self.format_weights.get(format_name, 0.0)
			if weight > 0 and len(dataset) > 0:
				# Calculate number of samples based on weight
				num_samples = max(1, int(len(dataset) * weight * 10))  # Scale up for better distribution

				for _ in range(num_samples):
					idx = random.randint(0, len(dataset) - 1)
					examples.append((format_name, idx))

		random.shuffle(examples)
		return examples

	def __len__(self) -> int:
		return len(self.examples)

	def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
		format_name, data_idx = self.examples[idx]

		# Get raw example
		raw_example = self.datasets[format_name][data_idx]

		# Format using appropriate formatter
		formatter = self.formatters[format_name]
		try:
			formatted_example = formatter.format_example(raw_example)
		except Exception as e:
			# Fallback to a simple pretraining example if formatting fails
			warnings.warn(f"Failed to format {format_name} example: {e}")
			formatted_example = {
				"input_ids": torch.zeros(self.config.max_length, dtype=torch.long),
				"attention_mask": torch.zeros(self.config.max_length, dtype=torch.long),
				"labels": torch.zeros(self.config.max_length, dtype=torch.long),
				"format_type": "pretraining"
			}

		return formatted_example

# ============================================================================
# ENHANCED LOSS FUNCTIONS
# ============================================================================

class DiffusionLoss(nn.Module):
	"""Multi-component loss for diffusion training with proper weighting"""

	def __init__(self, config: TrainingConfig):
		super().__init__()
		self.config = config

		# Loss weights
		self.generation_weight = config.generation_loss_weight
		self.consistency_weight = config.consistency_loss_weight
		self.memory_weight = config.memory_loss_weight
		self.confidence_weight = config.confidence_loss_weight

	def forward(self, outputs: Dict[str, torch.Tensor],
				targets: Dict[str, torch.Tensor],
				step_outputs: Optional[List[Dict[str, torch.Tensor]]] = None,
				diffusion_step: int = 0) -> Dict[str, torch.Tensor]:
		"""Compute total loss with all components (memory-optimized)"""

		losses = {}

		# 1. Generation loss (cross-entropy)
		logits = outputs["logits"]
		labels = targets.get("labels", targets.get("input_ids", None))

		if labels is None:
			raise ValueError("No labels found in targets")

		# Handle case where logits/labels shapes don't match
		if logits.size(1) != labels.size(1):
			min_len = min(logits.size(1), labels.size(1))
			logits = logits[:, :min_len, :]
			labels = labels[:, :min_len]

		generation_loss = F.cross_entropy(
			logits.view(-1, logits.size(-1)),
			labels.view(-1),
			ignore_index=-100,
			reduction='mean'
		)
		losses["generation"] = generation_loss

		# 2. Diffusion step losses (intermediate supervision) - only if step_outputs provided
		if step_outputs is not None and len(step_outputs) > 1:
			step_losses = []
			for i, step_output in enumerate(step_outputs[:-1]):  # Exclude final step
				if "logits" in step_output:
					step_logits = step_output["logits"]

					# Ensure shape compatibility
					if step_logits.size(1) != labels.size(1):
						min_len = min(step_logits.size(1), labels.size(1))
						step_logits = step_logits[:, :min_len, :]
						step_labels = labels[:, :min_len]
					else:
						step_labels = labels

					step_loss = F.cross_entropy(
						step_logits.view(-1, step_logits.size(-1)),
						step_labels.view(-1),
						ignore_index=-100,
						reduction='mean'
					)
					# Weight earlier steps less
					weight = (i + 1) / len(step_outputs)
					step_losses.append(weight * step_loss)

			if step_losses:
				losses["diffusion_steps"] = torch.stack(step_losses).mean()

		# 3. Consistency loss between consecutive steps - only if step_outputs provided
		if step_outputs is not None and len(step_outputs) > 1 and self.consistency_weight > 0:
			consistency_losses = []
			for i in range(len(step_outputs) - 1):
				if "hidden_states" in step_outputs[i] and "hidden_states" in step_outputs[i + 1]:
					prev_hidden = step_outputs[i]["hidden_states"]
					curr_hidden = step_outputs[i + 1]["hidden_states"]

					# Ensure same dimensions
					min_len = min(prev_hidden.size(1), curr_hidden.size(1))
					prev_hidden = prev_hidden[:, :min_len, :]
					curr_hidden = curr_hidden[:, :min_len, :]

					# Cosine similarity loss
					prev_norm = F.normalize(prev_hidden, dim=-1)
					curr_norm = F.normalize(curr_hidden, dim=-1)
					similarity = torch.sum(prev_norm * curr_norm, dim=-1)

					# Encourage high similarity (smooth transitions)
					consistency_loss = 1.0 - similarity.mean()
					consistency_losses.append(consistency_loss)

			if consistency_losses:
				losses["consistency"] = torch.stack(consistency_losses).mean()

		# 4. Confidence calibration loss
		if "confidence_scores" in outputs and self.confidence_weight > 0:
			confidence_scores = outputs["confidence_scores"]

			# Debug: Check confidence scores type
			if confidence_scores.dtype in [torch.long, torch.int, torch.int32, torch.int64]:
				print(f"WARNING: confidence_scores has integer dtype {confidence_scores.dtype}, converting to float")
				confidence_scores = confidence_scores.float()

			# Get prediction correctness
			predictions = torch.argmax(logits, dim=-1)
			correct_mask = (predictions == labels) & (labels != -100)

			# Ensure same shape for confidence scores
			if confidence_scores.size(1) != correct_mask.size(1):
				min_len = min(confidence_scores.size(1), correct_mask.size(1))
				confidence_scores = confidence_scores[:, :min_len]
				correct_mask = correct_mask[:, :min_len]

			# Confidence should be high for correct predictions
			confidence_target = correct_mask.float()

			# Convert probabilities back to logits for autocast-safe BCE
			# confidence_scores are sigmoid-activated, so we need logits
			confidence_logits = torch.logit(torch.clamp(confidence_scores, 1e-7, 1-1e-7))

			confidence_loss = F.binary_cross_entropy_with_logits(
				confidence_logits.view(-1),
				confidence_target.view(-1),
				reduction='mean'
			)
			losses["confidence"] = confidence_loss

		# 5. Memory reconstruction loss (if applicable)
		if "memory_reconstruction" in outputs and self.memory_weight > 0:
			memory_recon = outputs["memory_reconstruction"]
			memory_target = outputs.get("memory_target", labels)

			memory_loss = F.mse_loss(memory_recon, memory_target.float())
			losses["memory"] = memory_loss

		# Compute weighted total loss
		total_loss = self.generation_weight * losses["generation"]

		if "diffusion_steps" in losses:
			total_loss += 0.5 * losses["diffusion_steps"]

		if "consistency" in losses:
			total_loss += self.consistency_weight * losses["consistency"]

		if "confidence" in losses:
			total_loss += self.confidence_weight * losses["confidence"]

		if "memory" in losses:
			total_loss += self.memory_weight * losses["memory"]

		losses["total"] = total_loss

		return losses

# ============================================================================
# CURRICULUM LEARNING
# ============================================================================

class DiffusionCurriculum:
	"""Manages curriculum learning for diffusion complexity"""

	def __init__(self, config: TrainingConfig):
		self.config = config
		self.start_steps = config.start_diffusion_steps
		self.end_steps = config.end_diffusion_steps
		self.schedule = config.curriculum_schedule

	def get_diffusion_steps(self, current_step: int, max_steps: int) -> int:
		"""Get number of diffusion steps based on training progress"""
		if not self.config.diffusion_curriculum:
			return self.end_steps

		progress = min(current_step / max_steps, 1.0)

		if self.schedule == "linear":
			steps = self.start_steps + progress * (self.end_steps - self.start_steps)
		elif self.schedule == "exponential":
			# Slow start, rapid increase
			exp_progress = progress ** 2
			steps = self.start_steps + exp_progress * (self.end_steps - self.start_steps)
		elif self.schedule == "cosine":
			# Smooth S-curve
			cos_progress = 0.5 * (1 - math.cos(math.pi * progress))
			steps = self.start_steps + cos_progress * (self.end_steps - self.start_steps)
		else:
			steps = self.end_steps

		return max(1, min(int(steps), self.end_steps))

# ============================================================================
# ENHANCED EVALUATION SYSTEM
# ============================================================================

class DiffusionEvaluator:
	"""Comprehensive evaluation for diffusion models"""

	def __init__(self, tokenizer):
		self.tokenizer = tokenizer
		self.advanced_metrics = AdvancedEvaluationMetrics(tokenizer)

	def compute_diffusion_metrics(self, model_outputs: Dict[str, torch.Tensor],
								 targets: Dict[str, torch.Tensor]) -> Dict[str, float]:
		"""Compute diffusion-specific metrics"""
		metrics = {}

		# Standard language modeling metrics
		logits = model_outputs["logits"]
		labels = targets["labels"]

		# Perplexity
		loss = F.cross_entropy(
			logits.view(-1, logits.size(-1)),
			labels.view(-1),
			ignore_index=-100,
			reduction='mean'
		)
		metrics["perplexity"] = torch.exp(loss).item()

		# Accuracy
		predictions = torch.argmax(logits, dim=-1)
		correct = (predictions == labels) & (labels != -100)
		accuracy = correct.sum().float() / (labels != -100).sum().float()
		metrics["accuracy"] = accuracy.item()

		# Confidence metrics
		if "confidence_scores" in model_outputs:
			confidence_scores = model_outputs["confidence_scores"]
			# Ensure confidence scores are float type for mean() and std() operations
			if confidence_scores.dtype in [torch.long, torch.int, torch.int32, torch.int64]:
				confidence_scores = confidence_scores.float()
			avg_confidence = confidence_scores.mean().item()
			confidence_std = confidence_scores.std().item()

			metrics["avg_confidence"] = avg_confidence
			metrics["confidence_std"] = confidence_std

			# Enhanced confidence calibration (ECE)
			correct_predictions = (predictions == labels) & (labels != -100)
			ece = self.advanced_metrics.compute_expected_calibration_error(
				confidence_scores[labels != -100],
				correct_predictions[labels != -100].float()
			)
			metrics["expected_calibration_error"] = ece

			# Confidence-accuracy correlation
			if torch.sum(labels != -100) > 0:
				valid_confidence = confidence_scores[labels != -100]
				valid_accuracy = correct_predictions[labels != -100].float()
				if len(valid_confidence) > 1:
					correlation = torch.corrcoef(torch.stack([valid_confidence, valid_accuracy]))[0, 1]
					metrics["confidence_accuracy_correlation"] = correlation.item() if not torch.isnan(correlation) else 0.0

		# Diffusion-specific metrics
		if "num_steps" in model_outputs:
			metrics["avg_diffusion_steps"] = model_outputs["num_steps"]

		if "stop_reason" in model_outputs:
			metrics["early_stopping_rate"] = 1.0 if "early" in model_outputs["stop_reason"] else 0.0

		# Edit acceptance metrics
		if "edit_acceptance_rate" in model_outputs:
			metrics["edit_acceptance_rate"] = model_outputs["edit_acceptance_rate"]

		return metrics

	def evaluate_generation_quality(self, model: nn.Module,
								   eval_dataset: Dataset,
								   num_samples: int = 50) -> Dict[str, float]:
		"""Evaluate generation quality with BLEU/ROUGE scores"""
		model.eval()

		generated_texts = []
		reference_texts = []

		with torch.no_grad():
			for i in range(min(num_samples, len(eval_dataset))):
				try:
					example = eval_dataset[i]
					input_ids = example["input_ids"].unsqueeze(0)

					# Generate text
					generation_result = model.generate(
						input_ids=input_ids,
						max_length=50,
						use_diffusion=True,
						max_diffusion_steps=5  # Faster for evaluation
					)

					generated_ids = generation_result["generated_ids"][0]
					generated_text = self.tokenizer.decode(generated_ids, skip_special_tokens=True)

					# Reference text (original)
					reference_ids = example["labels"]
					reference_text = self.tokenizer.decode(
						reference_ids[reference_ids != -100],
						skip_special_tokens=True
					)

					generated_texts.append(generated_text)
					reference_texts.append(reference_text)

				except Exception as e:
					continue

		if generated_texts and reference_texts:
			return self.advanced_metrics.compute_generation_quality_metrics(
				generated_texts, reference_texts
			)
		else:
			return {}

# ============================================================================
# TRAINING UTILITIES
# ============================================================================

def setup_logging(config: TrainingConfig) -> logging.Logger:
	"""Setup logging configuration"""
	log_format = '%(asctime)s - %(levelname)s - %(name)s - %(message)s'
	logging.basicConfig(
		format=log_format,
		level=logging.INFO,
		handlers=[
			logging.FileHandler(Path(config.output_dir) / "training.log"),
			logging.StreamHandler()
		]
	)
	logger = logging.getLogger(__name__)

	if config.wandb_project and config.local_rank <= 0:
		wandb.init(
			project=config.wandb_project,
			name=config.run_name,
			config=config.__dict__,
			resume="allow"
		)

	return logger

def setup_distributed_training(config: TrainingConfig) -> None:
	"""Setup distributed training"""
	if config.local_rank != -1:
		torch.cuda.set_device(config.local_rank)
		dist.init_process_group(backend='nccl')
		config.world_size = dist.get_world_size()

def save_checkpoint(model: nn.Module, optimizer: torch.optim.Optimizer,
				   scheduler: Any, step: int, config: TrainingConfig,
				   losses: Dict[str, float], tokenizer: Any = None) -> None:
	"""Save training checkpoint with cleanup"""
	if config.local_rank <= 0:
		checkpoint = {
			'model_state_dict': model.state_dict(),
			'optimizer_state_dict': optimizer.state_dict(),
			'scheduler_state_dict': scheduler.state_dict(),
			'step': step,
			'config': config,
			'losses': losses,
			'tokenizer_vocab_size': len(tokenizer) if tokenizer else None
		}

		save_path = Path(config.output_dir) / f"checkpoint-{step}.pt"
		save_path.parent.mkdir(parents=True, exist_ok=True)
		torch.save(checkpoint, save_path)

		# Cleanup old checkpoints
		cleanup_checkpoints(config.output_dir, config.save_total_limit)

def cleanup_checkpoints(output_dir: str, save_total_limit: int) -> None:
	"""Remove old checkpoints to save disk space"""
	checkpoint_dir = Path(output_dir)
	checkpoints = list(checkpoint_dir.glob("checkpoint-*.pt"))

	if len(checkpoints) > save_total_limit:
		# Sort by step number
		checkpoints.sort(key=lambda x: int(x.stem.split('-')[1]))

		# Remove oldest checkpoints
		for checkpoint in checkpoints[:-save_total_limit]:
			checkpoint.unlink()

def load_checkpoint(model: nn.Module, optimizer: torch.optim.Optimizer,
				   scheduler: Any, checkpoint_path: str) -> int:
	"""Load training checkpoint with validation"""
	checkpoint = torch.load(checkpoint_path, map_location='cpu')

	try:
		model.load_state_dict(checkpoint['model_state_dict'])
		optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
		scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

		print(f"Successfully loaded checkpoint from step {checkpoint['step']}")
		return checkpoint['step']
	except Exception as e:
		warnings.warn(f"Failed to load checkpoint: {e}")
		return 0

def validate_inputs(batch: Dict[str, torch.Tensor], max_length: int = 1024) -> bool:
	"""Validate batch inputs for shape consistency"""
	if "input_ids" not in batch or "attention_mask" not in batch:
		return False
	input_ids, attention_mask = batch["input_ids"], batch["attention_mask"]
	if input_ids.dim() != 2 or attention_mask.dim() != 2:
		return False
	batch_size, seq_len = input_ids.shape
	if seq_len > max_length:
		return False
	if attention_mask.shape != input_ids.shape:
		return False
	return True

# ============================================================================
# ENHANCED TRAINER CLASS
# ============================================================================

class DiffusionTrainer:
	"""Enhanced training class for Diffusion LLM with advanced features"""

	def __init__(self, config: TrainingConfig):
		self.config = config
		self.logger = setup_logging(config)

		# Initialize scaler early to prevent timing issues
		try:
			self.scaler = torch.amp.GradScaler(enabled=config.fp16 or config.bf16)
			self.logger.info(f"Early initialized GradScaler with enabled={config.fp16 or config.bf16}")
		except Exception as e:
			self.logger.error(f"Failed to early initialize GradScaler: {e}")
			self.scaler = torch.amp.GradScaler(enabled=False)

		# MEMORY PROFILING: Initialize profiler
		self.profiler = None
		self.profile_every_n_steps = 50  # Profile every 50 steps
		self.memory_stats = []

		# Setup distributed training
		setup_distributed_training(config)

		# Initialize model components
		self._init_model()
		self._init_optimizer()
		self._init_data()
		self._init_training_components()

		# NEW: Initialize advanced components
		self._init_advanced_components()

		# Resume from checkpoint if specified
		if config.resume_from_checkpoint:
			self._resume_from_checkpoint()

	def _init_model(self) -> None:
		"""Initialize model and move to device with memory optimizations"""
		device = f"cuda:{self.config.local_rank}" if self.config.local_rank != -1 else "cuda"
		self.device = torch.device(device if torch.cuda.is_available() else "cpu")

		# Create model
		self.model = DiffusionLLM(self.config.model_config)

		# Enable gradient checkpointing if requested
		if self.config.use_gradient_checkpointing:
			self._enable_gradient_checkpointing()

		self.model = self.model.to(self.device)

		# Wrap with DDP if distributed
		if self.config.local_rank != -1:
			self.model = DDP(self.model, device_ids=[self.config.local_rank],
							find_unused_parameters=False)  # More memory efficient

	def _enable_gradient_checkpointing(self) -> None:
		"""Enable gradient checkpointing for memory efficiency"""
		if hasattr(self.model, 'transformer') and hasattr(self.model.transformer, 'layers'):
			for layer in self.model.transformer.layers:
				if hasattr(layer, 'forward'):
					# Store original forward method
					original_forward = layer.forward

					# Create checkpointed forward method
					def make_checkpointed_forward(orig_forward):
						def checkpointed_forward(*args, **kwargs):
							return tutils_checkpoint(
								orig_forward, *args, use_reentrant=False, **kwargs
							)
						return checkpointed_forward

					# Replace forward method
					layer.forward = make_checkpointed_forward(original_forward)

	def _cleanup_memory(self) -> None:
		"""Comprehensive memory cleanup"""
		# Clear CUDA cache
		if torch.cuda.is_available():
			torch.cuda.empty_cache()
			torch.cuda.synchronize()

		# Clear model memory caches
		if hasattr(self.model, 'memory_manager'):
			self.model.memory_manager.clear_all_memories()

		# Clear KV caches
		if hasattr(self.model, 'transformer'):
			for layer in self.model.transformer.layers:
				if hasattr(layer, 'attention') and hasattr(layer.attention, 'kv_cache'):
					if layer.attention.kv_cache is not None:
						layer.attention.kv_cache.clear()

		# Force garbage collection
		import gc
		gc.collect()

		self.logger.info(f"Model initialized with {sum(p.numel() for p in self.model.parameters())} parameters")

		# Mixed precision scaler
		try:
			self.scaler = torch.amp.GradScaler(enabled=self.config.fp16 or self.config.bf16)
			self.logger.info(f"Initialized GradScaler with enabled={self.config.fp16 or self.config.bf16}")
		except Exception as e:
			self.logger.error(f"Failed to initialize GradScaler: {e}")
			# Fallback: always create a scaler, even if disabled
			self.scaler = torch.amp.GradScaler(enabled=False)

	def _init_optimizer(self) -> None:
		"""Initialize optimizer and scheduler with parameter grouping"""
		# Separate learning rates for different components
		no_decay = ["bias", "LayerNorm.weight", "layer_norm.weight"]

		param_groups = [
			{
				'params': [p for n, p in self.model.named_parameters()
						  if 'memory' not in n and not any(nd in n for nd in no_decay)],
				'lr': self.config.learning_rate,
				'weight_decay': self.config.weight_decay
			},
			{
				'params': [p for n, p in self.model.named_parameters()
						  if 'memory' not in n and any(nd in n for nd in no_decay)],
				'lr': self.config.learning_rate,
				'weight_decay': 0.0
			},
			{
				'params': [p for n, p in self.model.named_parameters()
						  if 'memory' in n and not any(nd in n for nd in no_decay)],
				'lr': self.config.learning_rate * 0.5,  # Lower LR for memory components
				'weight_decay': self.config.weight_decay * 0.5
			},
			{
				'params': [p for n, p in self.model.named_parameters()
						  if 'memory' in n and any(nd in n for nd in no_decay)],
				'lr': self.config.learning_rate * 0.5,
				'weight_decay': 0.0
			}
		]

		self.optimizer = AdamW(param_groups, eps=1e-8, betas=(0.9, 0.95))

		# Learning rate scheduler
		self.scheduler = CosineAnnealingLR(
			self.optimizer,
			T_max=self.config.max_steps,
			eta_min=self.config.learning_rate * 0.1
		)

		# Warmup scheduler
		self.warmup_scheduler = LinearLR(
			self.optimizer,
			start_factor=0.1,
			total_iters=self.config.warmup_steps
		)

	def _init_data(self) -> None:
		"""Initialize datasets and data loaders"""
		# Training dataset
		self.train_dataset = MultiFormatDataset(self.config, split="train")

		# NEW: Dynamic batcher
		self.dynamic_batcher = DynamicBatcher(self.config)

		# Distributed sampler
		train_sampler = None
		if self.config.local_rank != -1:
			train_sampler = DistributedSampler(
				self.train_dataset,
				num_replicas=self.config.world_size,
				rank=self.config.local_rank,
				shuffle=True
			)

		self.train_loader = DataLoader(
			self.train_dataset,
			batch_size=self.config.batch_size,
			sampler=train_sampler,
			shuffle=(train_sampler is None),
			num_workers=self.config.dataloader_num_workers,
			pin_memory=True,
			drop_last=True  # Ensure consistent batch sizes
		)

		# Validation dataset
		try:
			self.val_dataset = MultiFormatDataset(self.config, split="validation")
			self.val_loader = DataLoader(
				self.val_dataset,
				batch_size=self.config.batch_size,
				shuffle=False,
				num_workers=2,
				pin_memory=True,
				drop_last=False
			)
		except Exception as e:
			self.val_loader = None
			self.logger.warning(f"Validation dataset not found: {e}")

	def _init_training_components(self) -> None:
		"""Initialize training-specific components"""
		self.loss_fn = DiffusionLoss(self.config)
		self.curriculum = DiffusionCurriculum(self.config)
		self.evaluator = DiffusionEvaluator(self.train_dataset.tokenizer)

		# Training state
		self.global_step = 0
		self.best_val_loss = float('inf')
		self.training_metrics = {
			"total_tokens": 0,
			"total_examples": 0,
			"format_counts": {"pretraining": 0, "instruction": 0, "conversation": 0, "agentic": 0}
		}

	def _init_advanced_components(self) -> None:
		"""Initialize advanced training components"""
		# NEW: Training dynamics analyzer
		self.dynamics_analyzer = TrainingDynamicsAnalyzer(self.config)

		# NEW: Data augmentation
		self.data_augmentation = DiffusionDataAugmentation(
			self.train_dataset.tokenizer, self.config
		)

		# NEW: Advanced metrics tracking
		self.edit_statistics = defaultdict(list)
		self.quality_progression = deque(maxlen=1000)  # Keep last 1000 quality scores

	def _resume_from_checkpoint(self) -> None:
		"""Resume training from checkpoint"""
		try:
			self.global_step = load_checkpoint(
				self.model, self.optimizer, self.scheduler,
				self.config.resume_from_checkpoint
			)
			self.logger.info(f"Resumed training from step {self.global_step}")
		except Exception as e:
			self.logger.error(f"Failed to resume from checkpoint: {e}")

	def train_step(self, batch: Dict[str, torch.Tensor]) -> Dict[str, float]:
		"""Execute single training step with enhanced diffusion integration and memory optimization"""
		self.model.train()

		# Ensure clean gradient state at the start of each step
		if hasattr(self.model, 'zero_grad'):
			self.model.zero_grad()

		# Clear any lingering gradients from previous steps
		self.optimizer.zero_grad(set_to_none=True)

		# Clear model internal states that might hold stale references
		if hasattr(self.model, 'memory_manager'):
			# Only clear if we're not in the middle of gradient accumulation
			if self.global_step % self.config.gradient_accumulation_steps == 0:
				self.model.memory_manager.clear_all_memories()

		# Validate inputs
		if not validate_inputs(batch, self.model.config.max_length):
			self.logger.error("Invalid batch shapes")
			return {"total": 0.0, "error": 1.0}

		# Move batch to device
		batch = {k: v.to(self.device, non_blocking=True) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}

		# Pad input_ids and attention_mask to max_length (in-place to save memory)
		seq_len = batch["input_ids"].shape[1]
		if seq_len < self.model.config.max_length:
			pad_len = self.model.config.max_length - seq_len
			# Use in-place operations to save memory
			batch["input_ids"] = torch.nn.functional.pad(
				batch["input_ids"], (0, pad_len), value=self.data_augmentation.pad_token_id
			)
			batch["attention_mask"] = torch.nn.functional.pad(
				batch["attention_mask"], (0, pad_len), value=0.0
			)

		# Get current diffusion steps from curriculum
		diffusion_steps = self.curriculum.get_diffusion_steps(self.global_step, self.config.max_steps)

		# Apply data augmentation if enabled (with memory optimization)
		if self.config.use_data_augmentation and random.random() < self.config.corruption_probability:
			augmented_batches = self.data_augmentation.augment_batch(batch, diffusion_steps)
			batch = random.choice(augmented_batches)
			# Clean up unused augmented batches immediately
			del augmented_batches

			# Validate augmented batch shapes
			if batch["input_ids"].shape != (batch["input_ids"].shape[0], self.model.config.max_length):
				self.logger.error(f"Augmented input_ids shape {batch['input_ids'].shape} does not match expected")
				return {"total": 0.0, "error": 1.0}
			if batch["attention_mask"].shape != batch["input_ids"].shape:
				self.logger.error(f"Shape mismatch: input_ids {batch['input_ids'].shape}, attention_mask {batch['attention_mask'].shape}")
				return {"total": 0.0, "error": 1.0}

		# Mixed precision training context
		dtype = torch.bfloat16 if self.config.bf16 else torch.float16
		precision_context = torch.amp.autocast('cuda', enabled=self.config.fp16 or self.config.bf16, dtype=dtype)

		#try:
		with precision_context:
			# MEMORY PROFILING: Track memory before model operations
			if self.global_step % self.profile_every_n_steps == 0:
				pre_model_stats = self._get_memory_stats()
				self.logger.info(f"PRE-MODEL MEMORY: {pre_model_stats.get('gpu_allocated_gb', 0):.2f}GB")

			# MEMORY FIX: Clear all model caches before forward pass
			if hasattr(self.model, 'transformer'):
				for layer in self.model.transformer.layers:
					if hasattr(layer, 'attention') and hasattr(layer.attention, 'kv_cache'):
						if layer.attention.kv_cache is not None:
							layer.attention.kv_cache.clear()

			# MEMORY FIX: Clear memory manager state every step to prevent accumulation
			if hasattr(self.model, 'memory_manager'):
				self.model.memory_manager.clear_all_memories()

			# Clear CUDA cache periodically to prevent memory fragmentation
			if self.global_step % 100 == 0:
				torch.cuda.empty_cache()

			# MEMORY PROFILING: Track memory before forward pass
			if self.global_step % self.profile_every_n_steps == 0:
				pre_forward_stats = self._get_memory_stats()
				self.logger.info(f"PRE-FORWARD MEMORY: {pre_forward_stats.get('gpu_allocated_gb', 0):.2f}GB")

			# CRITICAL MEMORY FIX: Profiling shows aten::empty_strided creating 132.48GB!
			# The DiffusionLLM model is creating massive intermediate tensors

			# AGGRESSIVE MEMORY OPTIMIZATION: Based on profiler showing 128GB+ tensor creation
			# The remaining issues are:
			# - aten::empty_like: 84.03 GB (tensor duplication)
			# - aten::to: 82.60 GB (type conversions)
			# - aten::empty: 72.86 GB (tensor allocations)

			# Force minimal precision and disable tensor caching
			with torch.amp.autocast('cuda', enabled=self.config.bf16, dtype=torch.bfloat16):
				# Set PyTorch to use minimal memory allocation strategies
				with torch.backends.cudnn.flags(enabled=True, benchmark=False, deterministic=True):
					# EMERGENCY FIX: Reduce batch size dynamically if memory is too high
					current_memory = torch.cuda.memory_allocated() / 1024**3
					if current_memory > 8.0:  # If already using >8GB
						# Use gradient checkpointing for this forward pass
						if hasattr(self.model, 'gradient_checkpointing_enable'):
							self.model.gradient_checkpointing_enable()

				# CRITICAL FIX: Multiple components creating massive tensors
				# 1. ConfidenceEstimator: 128GB from hidden_states.view(-1, hidden_dim)
				# 2. TokenEditor: Large tensor operations in generate_replacement_tokens_batch
				# 3. MemoryManager: update_memories creating large tensors

				# Temporarily replace memory-hungry methods
				original_estimate_method = None
				original_memory_update = None

				if hasattr(self.model.confidence_estimator, 'estimate_token_confidence_batch'):
					original_estimate_method = self.model.confidence_estimator.estimate_token_confidence_batch
					self.model.confidence_estimator.estimate_token_confidence_batch = self._memory_efficient_confidence_estimation

				if hasattr(self.model.memory_manager, 'update_memories'):
					original_memory_update = self.model.memory_manager.update_memories
					self.model.memory_manager.update_memories = self._memory_efficient_memory_update

				# Execute with minimal memory footprint
				outputs = self.model(
					input_ids=batch["input_ids"],
					attention_mask=batch["attention_mask"],
					max_steps=1,  # Single step only
					use_diffusion=False,  # Keep diffusion disabled for memory efficiency
					return_all_steps=False  # Never store intermediate states
				)

				# Restore original methods
				if original_estimate_method is not None:
					self.model.confidence_estimator.estimate_token_confidence_batch = original_estimate_method
				if original_memory_update is not None:
					self.model.memory_manager.update_memories = original_memory_update

				# Disable gradient checkpointing after forward pass
				if hasattr(self.model, 'gradient_checkpointing_disable'):
					self.model.gradient_checkpointing_disable()

				# IMMEDIATE MEMORY FIX: Aggressively clear intermediate tensors after forward pass
				# This should prevent the 9.79GB memory leak we observed
				if hasattr(self.model, 'clear_intermediate_states'):
					self.model.clear_intermediate_states()

				# Clear any cached computations in model components
				if hasattr(self.model, 'token_editor'):
					if hasattr(self.model.token_editor, 'clear_cache'):
						self.model.token_editor.clear_cache()

				if hasattr(self.model, 'confidence_estimator'):
					if hasattr(self.model.confidence_estimator, 'clear_cache'):
						self.model.confidence_estimator.clear_cache()

				# CRITICAL MEMORY FIX: Force immediate cleanup of ALL intermediate tensors
				# The profiling showed 9.79GB of unaccounted memory after forward pass

				# Clear model internal states that might be holding references
				if hasattr(self.model, 'transformer'):
					for layer in self.model.transformer.layers:
						# Clear attention intermediate states
						if hasattr(layer, 'attention'):
							if hasattr(layer.attention, '_cached_key'):
								layer.attention._cached_key = None
							if hasattr(layer.attention, '_cached_value'):
								layer.attention._cached_value = None
							if hasattr(layer.attention, '_attention_weights'):
								layer.attention._attention_weights = None

						# Clear feed-forward intermediate states
						if hasattr(layer, 'feed_forward'):
							if hasattr(layer.feed_forward, '_intermediate'):
								layer.feed_forward._intermediate = None

				# Clear any autograd graph references
				if hasattr(outputs, 'logits') and outputs.logits.grad_fn is not None:
					# Detach from computation graph to free intermediate tensors
					outputs.logits = outputs.logits.detach()

				# Force immediate garbage collection of intermediate tensors
				torch.cuda.empty_cache()
				gc.collect()

				# Double-check memory after cleanup
				if self.global_step % self.profile_every_n_steps == 0:
					post_cleanup_stats = self._get_memory_stats()
					self.logger.info(f"POST-CLEANUP MEMORY: {post_cleanup_stats.get('gpu_allocated_gb', 0):.2f}GB")

			# MEMORY PROFILING: Track memory after forward pass
			if self.global_step % self.profile_every_n_steps == 0:
				post_forward_stats = self._get_memory_stats()
				forward_memory_increase = post_forward_stats.get('gpu_allocated_gb', 0) - pre_forward_stats.get('gpu_allocated_gb', 0)
				self.logger.info(f"POST-FORWARD MEMORY: {post_forward_stats.get('gpu_allocated_gb', 0):.2f}GB (+{forward_memory_increase:.2f}GB)")

				# Analyze model component memory usage
				self._analyze_model_memory_usage()

			# Only track essential dynamics to save memory
			self._track_diffusion_dynamics_minimal(outputs, diffusion_steps)
			losses = self.loss_fn(outputs, batch, None, diffusion_steps)  # Pass None for step_outputs

		# Gradient scaling and backward pass
		if not hasattr(self, 'scaler') or self.scaler is None:
			self.logger.error("Scaler not initialized! Creating fallback scaler.")
			self.scaler = torch.amp.GradScaler(enabled=self.config.fp16 or self.config.bf16)

		# Create a fresh loss tensor to avoid gradient graph reuse issues
		# This ensures we have a clean computational graph for each backward pass
		total_loss_value = losses["total"]
		# Re-enable gradient accumulation scaling for proper averaging
		loss_for_backward = total_loss_value / self.config.gradient_accumulation_steps

		# Ensure the loss tensor is properly connected to the computational graph
		if not loss_for_backward.requires_grad:
			self.logger.warning(f"Step {self.global_step}: Loss tensor does not require gradients!")
			return {"total": total_loss_value.detach().item(), "error": 1.0}

		scaled_loss = self.scaler.scale(loss_for_backward)

		# MEMORY PROFILING: Track memory before backward pass
		if self.global_step % self.profile_every_n_steps == 0:
			pre_backward_stats = self._get_memory_stats()
			self.logger.info(f"PRE-BACKWARD MEMORY: {pre_backward_stats.get('gpu_allocated_gb', 0):.2f}GB")

		try:
			# MEMORY FIX: Never use retain_graph - it causes massive memory leaks
			# Simple backward pass without retaining graph
			scaled_loss.backward()
		except RuntimeError as e:
			if "backward through the graph a second time" in str(e):
				self.logger.error(f"Gradient computation error at step {self.global_step}: {str(e)}")
				self.logger.error("This might be due to tensor reuse or gradient accumulation issues")
				# Try to recover by skipping this step
				return {"total": total_loss_value.detach().item(), "error": 1.0}
			else:
				raise e

		# MEMORY PROFILING: Track memory after backward pass
		if self.global_step % self.profile_every_n_steps == 0:
			post_backward_stats = self._get_memory_stats()
			backward_memory_increase = post_backward_stats.get('gpu_allocated_gb', 0) - pre_backward_stats.get('gpu_allocated_gb', 0)
			self.logger.info(f"POST-BACKWARD MEMORY: {post_backward_stats.get('gpu_allocated_gb', 0):.2f}GB (+{backward_memory_increase:.2f}GB)")

		# Update metrics with minimal memory usage
		self._update_training_metrics_minimal(batch, outputs)

		# Store loss value for return before cleanup
		final_loss_value = total_loss_value.detach().item()

		# MEMORY FIX: Proper tensor cleanup - just delete references
		# Don't modify tensors that are still in use
		outputs = None
		scaled_loss = None
		loss_for_backward = None
		total_loss_value = None

		# Initialize grad_norm for consistent return values
		grad_norm = 0.0

		# Handle gradient accumulation properly
		# Re-enable gradient accumulation for better throughput
		if (self.global_step + 1) % self.config.gradient_accumulation_steps == 0:
			# MEMORY PROFILING: Track memory before optimizer step
			if self.global_step % self.profile_every_n_steps == 0:
				pre_optimizer_stats = self._get_memory_stats()
				self.logger.info(f"PRE-OPTIMIZER MEMORY: {pre_optimizer_stats.get('gpu_allocated_gb', 0):.2f}GB")

			# Gradient clipping
			self.scaler.unscale_(self.optimizer)
			grad_norm = torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.max_grad_norm)

			# Optimizer step
			self.scaler.step(self.optimizer)
			self.scaler.update()

			# Clear gradients and ensure no lingering references
			self.optimizer.zero_grad(set_to_none=True)  # More memory efficient than zero_grad()

			# MEMORY PROFILING: Track memory after optimizer step
			if self.global_step % self.profile_every_n_steps == 0:
				post_optimizer_stats = self._get_memory_stats()
				optimizer_memory_change = post_optimizer_stats.get('gpu_allocated_gb', 0) - pre_optimizer_stats.get('gpu_allocated_gb', 0)
				self.logger.info(f"POST-OPTIMIZER MEMORY: {post_optimizer_stats.get('gpu_allocated_gb', 0):.2f}GB ({optimizer_memory_change:+.2f}GB)")

			# Reduced cleanup for speed - only clear cache occasionally
			if self.global_step % 100 == 0:  # Only every 100 steps instead of every step
				torch.cuda.empty_cache() if torch.cuda.is_available() else None

			# Memory manager is already cleared every step above, no need for additional clearing
		else:
			# MEMORY FIX: For gradient accumulation steps, ensure we don't leak memory
			# Clear any temporary variables that might accumulate
			pass

			# Learning rate scheduling
			if self.global_step < self.config.warmup_steps:
				self.warmup_scheduler.step()
			else:
				self.scheduler.step()

			losses["grad_norm"] = grad_norm.item() if isinstance(grad_norm, torch.Tensor) else grad_norm

		# Update losses with the stored final loss value
		losses["total"] = final_loss_value

		return losses

	def _get_memory_stats(self) -> Dict[str, float]:
		"""Get detailed memory statistics"""
		stats = {}

		if torch.cuda.is_available():
			# GPU Memory
			stats['gpu_allocated_gb'] = torch.cuda.memory_allocated() / 1024**3
			stats['gpu_reserved_gb'] = torch.cuda.memory_reserved() / 1024**3
			stats['gpu_max_allocated_gb'] = torch.cuda.max_memory_allocated() / 1024**3
			stats['gpu_max_reserved_gb'] = torch.cuda.max_memory_reserved() / 1024**3

			# Memory efficiency
			stats['gpu_utilization'] = stats['gpu_allocated_gb'] / stats['gpu_reserved_gb'] if stats['gpu_reserved_gb'] > 0 else 0

		# CPU Memory
		process = psutil.Process()
		memory_info = process.memory_info()
		stats['cpu_memory_gb'] = memory_info.rss / 1024**3
		stats['cpu_memory_percent'] = process.memory_percent()

		return stats

	def _profile_memory_usage(self, step: int, phase: str = ""):
		"""Profile memory usage and log detailed statistics"""
		if step % self.profile_every_n_steps != 0:
			return

		stats = self._get_memory_stats()
		stats['step'] = step
		stats['phase'] = phase
		self.memory_stats.append(stats)

		# Log current memory usage
		self.logger.info(f"MEMORY PROFILE [Step {step}] {phase}:")
		self.logger.info(f"  GPU: {stats.get('gpu_allocated_gb', 0):.2f}GB allocated, {stats.get('gpu_reserved_gb', 0):.2f}GB reserved")
		self.logger.info(f"  GPU Utilization: {stats.get('gpu_utilization', 0):.1%}")
		self.logger.info(f"  CPU: {stats.get('cpu_memory_gb', 0):.2f}GB ({stats.get('cpu_memory_percent', 0):.1f}%)")

		# Analyze memory growth
		if len(self.memory_stats) > 1:
			prev_stats = self.memory_stats[-2]
			gpu_growth = stats.get('gpu_allocated_gb', 0) - prev_stats.get('gpu_allocated_gb', 0)
			if gpu_growth > 0.1:  # More than 100MB growth
				self.logger.warning(f"  GPU Memory Growth: +{gpu_growth:.2f}GB since last profile")

	def _memory_efficient_confidence_estimation(self, hidden_states: torch.Tensor,
											   logits: torch.Tensor,
											   mc_samples: int = 5) -> torch.Tensor:
		"""Memory-efficient confidence estimation that doesn't create massive tensors"""
		batch_size, seq_len, hidden_dim = hidden_states.shape

		# CRITICAL FIX: Process in smaller chunks instead of flattening everything
		# The original method does hidden_states.view(-1, hidden_dim) which creates 132GB tensors!

		chunk_size = min(32, seq_len)  # Process 32 tokens at a time
		confidence_scores = torch.zeros(batch_size, seq_len, device=hidden_states.device, dtype=hidden_states.dtype)

		for batch_idx in range(batch_size):
			for start_idx in range(0, seq_len, chunk_size):
				end_idx = min(start_idx + chunk_size, seq_len)

				# Process small chunk instead of entire sequence
				chunk_hidden = hidden_states[batch_idx:batch_idx+1, start_idx:end_idx, :]  # [1, chunk_size, hidden_dim]
				chunk_flat = chunk_hidden.view(-1, hidden_dim)  # Much smaller tensor

				# Use the confidence head from the original estimator
				chunk_confidence = self.model.confidence_estimator.confidence_head(chunk_flat)
				confidence_scores[batch_idx, start_idx:end_idx] = chunk_confidence.view(-1)

				# Clear intermediate tensors immediately
				del chunk_hidden, chunk_flat, chunk_confidence

		return confidence_scores

	def _memory_efficient_memory_update(self, input_ids: torch.Tensor,
									   hidden_states: torch.Tensor,
									   step: int) -> None:
		"""Memory-efficient memory update that avoids large tensor operations"""
		# Skip memory updates during training to prevent massive tensor creation
		# The MemoryManager.update_memories() method likely creates large tensors
		# for vectorized operations across the entire batch and sequence

		# Only update memory every few steps to reduce memory pressure
		if step % 5 == 0:  # Update every 5 steps instead of every step
			batch_size, seq_len = input_ids.shape

			# Process in smaller chunks to avoid large tensor creation
			chunk_size = min(16, batch_size)  # Process 16 samples at a time

			for start_idx in range(0, batch_size, chunk_size):
				end_idx = min(start_idx + chunk_size, batch_size)

				# Process small chunk
				chunk_input_ids = input_ids[start_idx:end_idx]
				chunk_hidden_states = hidden_states[start_idx:end_idx]

				# Call original method with small chunk (if it exists)
				if hasattr(self.model.memory_manager, '_original_update_memories'):
					self.model.memory_manager._original_update_memories(
						chunk_input_ids, chunk_hidden_states, step
					)

				# Clear chunk tensors
				del chunk_input_ids, chunk_hidden_states

	def _start_torch_profiler(self, step: int):
		"""Start PyTorch profiler for detailed operation analysis"""
		if self.profiler is not None:
			return

		self.profiler = torch.profiler.profile(
			activities=[
				torch.profiler.ProfilerActivity.CPU,
				torch.profiler.ProfilerActivity.CUDA,
			],
			record_shapes=True,
			profile_memory=True,
			with_stack=True,
			schedule=torch.profiler.schedule(
				wait=1,
				warmup=1,
				active=3,
				repeat=1
			),
			on_trace_ready=self._save_profiler_trace
		)
		self.profiler.start()
		self.logger.info(f"Started PyTorch profiler at step {step}")

	def _save_profiler_trace(self, prof):
		"""Save profiler trace and analyze memory usage"""
		# Save trace file
		trace_file = f"profiler_trace_step_{self.global_step}.json"
		prof.export_chrome_trace(trace_file)
		self.logger.info(f"Saved profiler trace: {trace_file}")

		# Analyze memory usage in detail
		self.logger.info("=" * 80)
		self.logger.info("DETAILED MEMORY ANALYSIS FROM PYTORCH PROFILER:")
		self.logger.info("=" * 80)

		# Get key averages sorted by memory usage
		key_averages = prof.key_averages()

		# Top memory consuming operations
		self.logger.info("TOP 15 MEMORY CONSUMING OPERATIONS:")
		memory_events = key_averages.table(
			sort_by="cuda_memory_usage",
			row_limit=15,
			max_name_column_width=50
		)
		for line in memory_events.split('\n'):
			if line.strip():
				self.logger.info(f"  {line}")

		# Top time consuming operations
		self.logger.info("\nTOP 15 TIME CONSUMING OPERATIONS:")
		time_events = key_averages.table(
			sort_by="cuda_time_total",
			row_limit=15,
			max_name_column_width=50
		)
		for line in time_events.split('\n'):
			if line.strip():
				self.logger.info(f"  {line}")

		# Memory summary - fix attribute error
		total_memory = 0
		high_memory_ops = []

		for event in key_averages:
			# Use the correct attribute name
			if hasattr(event, 'self_cuda_memory_usage'):
				memory_usage = event.self_cuda_memory_usage
			elif hasattr(event, 'cuda_memory_usage'):
				memory_usage = event.cuda_memory_usage
			else:
				memory_usage = 0

			total_memory += memory_usage

			# Find operations with highest memory per call
			if event.count > 0 and memory_usage > 0:
				memory_per_call = memory_usage / event.count
				if memory_per_call > 100 * 1024 * 1024:  # More than 100MB per call
					high_memory_ops.append((event.key, memory_per_call, event.count))

		self.logger.info(f"\nTOTAL PROFILED MEMORY USAGE: {total_memory / 1024**3:.2f}GB")

		if high_memory_ops:
			self.logger.info("\nHIGH MEMORY PER CALL OPERATIONS (>100MB per call):")
			high_memory_ops.sort(key=lambda x: x[1], reverse=True)
			for op_name, memory_per_call, count in high_memory_ops[:10]:
				self.logger.info(f"  {op_name}: {memory_per_call / 1024**3:.2f}GB per call ({count} calls)")

		self.logger.info("=" * 80)

	def _stop_torch_profiler(self):
		"""Stop PyTorch profiler"""
		if self.profiler is not None:
			self.profiler.stop()
			self.profiler = None

	def _analyze_model_memory_usage(self):
		"""Analyze memory usage by model components"""
		if not torch.cuda.is_available():
			return

		self.logger.info("ANALYZING MODEL COMPONENT MEMORY USAGE:")

		# Model parameters
		param_memory = sum(p.numel() * p.element_size() for p in self.model.parameters()) / 1024**3
		self.logger.info(f"  Model Parameters: {param_memory:.2f}GB")

		# Gradients
		grad_memory = sum(p.grad.numel() * p.grad.element_size() for p in self.model.parameters() if p.grad is not None) / 1024**3
		self.logger.info(f"  Gradients: {grad_memory:.2f}GB")

		# Optimizer states
		optimizer_memory = 0
		for group in self.optimizer.param_groups:
			for p in group['params']:
				if p in self.optimizer.state:
					state = self.optimizer.state[p]
					for key, value in state.items():
						if isinstance(value, torch.Tensor):
							optimizer_memory += value.numel() * value.element_size()
		optimizer_memory /= 1024**3
		self.logger.info(f"  Optimizer States: {optimizer_memory:.2f}GB")

		# Model component analysis
		if hasattr(self.model, 'transformer'):
			# Transformer layers
			layer_count = len(self.model.transformer.layers) if hasattr(self.model.transformer, 'layers') else 0
			self.logger.info(f"  Transformer Layers: {layer_count}")

			# KV Cache analysis
			kv_cache_memory = 0
			for i, layer in enumerate(self.model.transformer.layers):
				if hasattr(layer, 'attention') and hasattr(layer.attention, 'kv_cache'):
					if layer.attention.kv_cache is not None:
						cache_size = layer.attention.kv_cache.get_memory_usage() if hasattr(layer.attention.kv_cache, 'get_memory_usage') else 0
						kv_cache_memory += cache_size
			self.logger.info(f"  KV Cache: {kv_cache_memory / 1024**3:.2f}GB")

		# Memory Manager analysis
		if hasattr(self.model, 'memory_manager'):
			mm_memory = 0
			if hasattr(self.model.memory_manager, 'get_memory_usage'):
				mm_memory = self.model.memory_manager.get_memory_usage()
			self.logger.info(f"  Memory Manager: {mm_memory / 1024**3:.2f}GB")

		# Calculate unaccounted memory
		total_accounted = param_memory + grad_memory + optimizer_memory + (kv_cache_memory / 1024**3) + (mm_memory / 1024**3)
		current_allocated = torch.cuda.memory_allocated() / 1024**3
		unaccounted = current_allocated - total_accounted
		self.logger.info(f"  TOTAL ACCOUNTED: {total_accounted:.2f}GB")
		self.logger.info(f"  CURRENT ALLOCATED: {current_allocated:.2f}GB")
		self.logger.info(f"  UNACCOUNTED MEMORY: {unaccounted:.2f}GB")

		if unaccounted > 1.0:  # More than 1GB unaccounted
			self.logger.warning(f"  WARNING: {unaccounted:.2f}GB of unaccounted memory - possible memory leak!")

			# Try to identify the source
			self.logger.info("  INVESTIGATING UNACCOUNTED MEMORY:")

			# Check for large tensors in model
			large_tensors = []
			for name, tensor in self.model.named_parameters():
				size_gb = tensor.numel() * tensor.element_size() / 1024**3
				if size_gb > 0.1:  # Larger than 100MB
					large_tensors.append((name, size_gb))

			large_tensors.sort(key=lambda x: x[1], reverse=True)
			for name, size_gb in large_tensors[:10]:  # Top 10
				self.logger.info(f"    {name}: {size_gb:.2f}GB")

			# Force garbage collection and check again
			gc.collect()
			torch.cuda.empty_cache()
			post_gc_allocated = torch.cuda.memory_allocated() / 1024**3
			freed_memory = current_allocated - post_gc_allocated
			if freed_memory > 0.1:
				self.logger.info(f"  FREED {freed_memory:.2f}GB after garbage collection")

	def _track_diffusion_dynamics_minimal(self, outputs: Dict[str, torch.Tensor], diffusion_steps: int) -> None:
		"""Track essential diffusion training dynamics with minimal memory usage"""
		# Only track essential metrics to save memory
		if "confidence_scores" in outputs:
			confidence_scores = outputs["confidence_scores"]
			# Ensure confidence scores are float type for mean() operation
			if confidence_scores.dtype in [torch.long, torch.int, torch.int32, torch.int64]:
				confidence_scores = confidence_scores.float()
			quality_score = confidence_scores.mean().item()
			self.quality_progression.append(quality_score)

			# Track only final step dynamics to save memory
			state_info = {
				"global_step": self.global_step,
				"quality_score": quality_score,
				"diffusion_steps": diffusion_steps,
				"memory_utilization": 0.0
			}
			self.dynamics_analyzer.track_diffusion_step(diffusion_steps - 1, state_info)

	def _track_diffusion_dynamics(self, outputs: Dict[str, torch.Tensor],
								 step_outputs: List[Dict[str, torch.Tensor]],
								 diffusion_steps: int) -> None:
		"""Track diffusion training dynamics (legacy method for compatibility)"""
		# Track quality progression
		if "confidence_scores" in outputs:
			confidence_scores = outputs["confidence_scores"]
			# Ensure confidence scores are float type for mean() operation
			if confidence_scores.dtype in [torch.long, torch.int, torch.int32, torch.int64]:
				confidence_scores = confidence_scores.float()
			quality_score = confidence_scores.mean().item()
			self.quality_progression.append(quality_score)

			# Track per-step dynamics
			for i, step_output in enumerate(step_outputs):
				state_info = {
					"global_step": self.global_step,
					"quality_score": quality_score,
					"confidence_scores": step_output.get("confidence_scores", torch.tensor([0.5])),
					"num_edits": 0,  # Will be updated when edit info is available
					"edit_acceptance_rate": 1.0,  # Default
					"memory_utilization": 0.0  # Will be updated
				}

				self.dynamics_analyzer.track_diffusion_step(i, state_info)

	def _update_training_metrics_minimal(self, batch: Dict[str, torch.Tensor],
										outputs: Dict[str, torch.Tensor]) -> None:
		"""Update training metrics with minimal memory usage"""
		batch_size = batch["input_ids"].size(0)
		seq_len = batch["input_ids"].size(1)

		self.training_metrics["total_tokens"] += batch_size * seq_len
		self.training_metrics["total_examples"] += batch_size

		# Update format counts if available (without storing references)
		if "format_type" in batch:
			format_types = batch["format_type"]
			# Handle both single format and batched formats
			if isinstance(format_types, (list, tuple)):
				# Count each format type in the batch
				for format_type in format_types:
					if format_type in self.training_metrics["format_counts"]:
						self.training_metrics["format_counts"][format_type] += 1
					else:
						self.training_metrics["format_counts"][format_type] = 1
			else:
				# Single format type for entire batch
				format_type = format_types
				if format_type in self.training_metrics["format_counts"]:
					self.training_metrics["format_counts"][format_type] += batch_size
				else:
					self.training_metrics["format_counts"][format_type] = batch_size

	def _update_training_metrics(self, batch: Dict[str, torch.Tensor],
								outputs: Dict[str, torch.Tensor]) -> None:
		"""Update training metrics (legacy method for compatibility)"""
		batch_size = batch["input_ids"].size(0)
		seq_len = batch["input_ids"].size(1)

		self.training_metrics["total_tokens"] += batch_size * seq_len
		self.training_metrics["total_examples"] += batch_size

		# Update format counts if available
		if "format_type" in batch:
			format_type = batch["format_type"]
			if format_type in self.training_metrics["format_counts"]:
				self.training_metrics["format_counts"][format_type] += batch_size

	@torch.no_grad()
	def evaluate(self) -> Dict[str, float]:
		"""Enhanced evaluation with advanced metrics"""
		if self.val_loader is None:
			return {}

		self.model.eval()

		total_losses = {}
		all_metrics = []
		num_batches = 0

		for batch in tqdm(self.val_loader, desc="Evaluating", disable=self.config.local_rank > 0):
			if not validate_inputs(batch):
				continue

			batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}

			try:
				# Standard forward pass for evaluation (faster)
				outputs = self.model(
					input_ids=batch["input_ids"],
					attention_mask=batch["attention_mask"],
					use_diffusion=False  # Use standard mode for faster evaluation
				)

				# Compute losses
				losses = self.loss_fn(outputs, batch)

				# Compute metrics
				metrics = self.evaluator.compute_diffusion_metrics(outputs, batch)
				all_metrics.append(metrics)

				# Accumulate losses
				for k, v in losses.items():
					if k not in total_losses:
						total_losses[k] = 0.0
					total_losses[k] += v.item()

				num_batches += 1

				if num_batches >= 100:  # Limit evaluation time
					break

			except Exception as e:
				self.logger.warning(f"Evaluation step failed: {e}")
				continue

		if num_batches == 0:
			return {}

		# Average losses
		avg_losses = {f"val_{k}": v / num_batches for k, v in total_losses.items()}

		# Average metrics
		if all_metrics:
			avg_metrics = {}
			for key in all_metrics[0].keys():
				avg_metrics[f"val_{key}"] = sum(m[key] for m in all_metrics) / len(all_metrics)
			avg_losses.update(avg_metrics)

		# NEW: Add generation quality metrics
		if self.config.compute_generation_metrics and self.config.local_rank <= 0:
			generation_metrics = self.evaluator.evaluate_generation_quality(
				self.model, self.val_dataset, self.config.max_generation_samples
			)
			generation_metrics = {f"val_gen_{k}": v for k, v in generation_metrics.items()}
			avg_losses.update(generation_metrics)

		return avg_losses

	def train(self) -> None:
		"""Enhanced training loop with advanced monitoring"""
		self.logger.info("Starting Enhanced Diffusion LLM training...")

		# Create output directory
		Path(self.config.output_dir).mkdir(parents=True, exist_ok=True)

		data_iter = iter(self.train_loader)
		moving_avg_loss = None
		best_checkpoint_step = 0

		# Create progress bar
		progress_bar = tqdm(
			range(self.global_step, self.config.max_steps),
			desc="Training",
			initial=self.global_step,
			total=self.config.max_steps,
			unit="step",
			dynamic_ncols=True,
			leave=True
		)

		for step in progress_bar:
			self.global_step = step

			# MEMORY PROFILING: Profile memory before training step
			self._profile_memory_usage(step, "before_step")

			# Start detailed profiler every 100 steps
			if step % 100 == 0 and step > 0:
				self._start_torch_profiler(step)

			# IMMEDIATE FIX: Start profiler on step 1 to catch the memory leak
			if step == 1:
				self._start_torch_profiler(step)

			try:
				batch = next(data_iter)
			except StopIteration:
				data_iter = iter(self.train_loader)
				batch = next(data_iter)

			# MEMORY PROFILING: Profile memory after batch loading
			self._profile_memory_usage(step, "after_batch_load")

			# Training step
			start_time = time.time()

			# Profile during training step if profiler is active
			if self.profiler is not None:
				self.profiler.step()

			losses = self.train_step(batch)
			step_time = time.time() - start_time

			# MEMORY FIX: Clear batch reference after use
			batch = None

			# MEMORY PROFILING: Profile memory after training step
			self._profile_memory_usage(step, "after_train_step")

			# Stop profiler after a few steps to avoid overhead
			if step % 100 == 5 and self.profiler is not None:
				self._stop_torch_profiler()

			# Handle training errors
			if losses.get("error", 0) > 0:
				self.logger.warning(f"Training error at step {step}")
				continue

			# Moving average loss
			if moving_avg_loss is None:
				moving_avg_loss = losses["total"]
			else:
				moving_avg_loss = 0.9 * moving_avg_loss + 0.1 * losses["total"]

			# Update progress bar with current metrics
			lr = self.optimizer.param_groups[0]['lr']
			progress_bar.set_postfix({
				'Loss': f'{moving_avg_loss:.4f}',
				'LR': f'{lr:.2e}',
				'Time': f'{step_time:.2f}s'
			})

			# Logging
			if step % self.config.logging_steps == 0:
				lr = self.optimizer.param_groups[0]['lr']
				diffusion_steps = self.curriculum.get_diffusion_steps(step, self.config.max_steps)

				# Memory usage
				memory_usage = {}
				if hasattr(self.model, 'get_memory_states'):
					memory_usage = self.model.get_memory_states()

				# NEW: Advanced training dynamics
				convergence_analysis = self.dynamics_analyzer.analyze_convergence_patterns()

				log_dict = {
					"step": step,
					"learning_rate": lr,
					"diffusion_steps": diffusion_steps,
					"moving_avg_loss": moving_avg_loss,
					"step_time": step_time,
					"tokens_per_sec": self.training_metrics["total_tokens"] / max((step + 1), 1),
					**losses,
					**memory_usage,
					**self.training_metrics["format_counts"],
					**{f"dynamics_{k}": v for k, v in convergence_analysis.items() if isinstance(v, (int, float))}
				}

				self.logger.info(
					f"Step {step}: Loss = {moving_avg_loss:.4f}, "
					f"LR = {lr:.2e}, Diff Steps = {diffusion_steps}, "
					f"Time = {step_time:.2f}s"
				)

				if self.config.wandb_project and self.config.local_rank <= 0:
					wandb.log(log_dict, step=step)

			# Evaluation
			if step > 0 and step % self.config.eval_steps == 0:
				eval_start_time = time.time()
				val_losses = self.evaluate()
				eval_time = time.time() - eval_start_time

				if val_losses and self.config.local_rank <= 0:
					self.logger.info(
						f"Validation - Step {step}: {val_losses} "
						f"(eval time: {eval_time:.2f}s)"
					)

					if self.config.wandb_project:
						wandb.log(val_losses, step=step)

					# Save best model
					val_loss = val_losses.get("val_total", float('inf'))
					if val_loss < self.best_val_loss:
						self.best_val_loss = val_loss
						best_checkpoint_step = step
						save_checkpoint(
							self.model, self.optimizer, self.scheduler,
							step, self.config, val_losses, self.train_dataset.tokenizer
						)
						self.logger.info(f"New best validation loss: {val_loss:.4f}")

			# Regular checkpointing
			if step > 0 and step % self.config.save_steps == 0:
				save_checkpoint(
					self.model, self.optimizer, self.scheduler,
					step, self.config, losses, self.train_dataset.tokenizer
				)

				# NEW: Save training analysis report
				if self.config.local_rank <= 0:
					training_report = self.dynamics_analyzer.generate_training_report()
					report_path = Path(self.config.output_dir) / f"training_report_step_{step}.json"
					with open(report_path, 'w') as f:
						json.dump(training_report, f, indent=2, default=str)

				# Cleanup memory after checkpointing
				self._cleanup_memory()

			# Periodic memory cleanup
			if step % 500 == 0:
				self._cleanup_memory()

		# Close progress bar
		progress_bar.close()

		# Final training analysis
		if self.config.local_rank <= 0:
			final_report = self.dynamics_analyzer.generate_training_report()
			self.logger.info(f"Training completed! Final analysis: {final_report}")

		self.logger.info(
			f"Training completed! Best checkpoint at step {best_checkpoint_step} "
			f"with validation loss {self.best_val_loss:.4f}"
		)

# ============================================================================
# TRAINING SCRIPT
# ============================================================================

def main():
	"""Main training function with enhanced configuration"""
	# Configuration
	config = TrainingConfig(
		# Model config
		model_config=DiffusionConfig(
			vocab_size=50257,
			hidden_dim=768,
			num_layers=12,
			num_heads=12,
			max_length=1024,
			diffusion_steps=14,
			use_kv_cache=True,
			confidence_calibration=True
		),

		# Training config
		learning_rate=5e-5,
		batch_size=8,
		max_steps=100000,
		warmup_steps=1000,
		gradient_accumulation_steps=4,

		# Curriculum learning
		diffusion_curriculum=True,
		start_diffusion_steps=3,
		end_diffusion_steps=14,
		curriculum_schedule="cosine",

		# Data config
		data_dir="./data",
		output_dir="./checkpoints",

		# Format weights
		pretraining_weight=0.4,
		instruction_weight=0.3,
		conversation_weight=0.2,
		agentic_weight=0.1,

		# Loss weights
		generation_loss_weight=1.0,
		consistency_loss_weight=0.1,
		memory_loss_weight=0.05,
		confidence_loss_weight=0.05,

		# Evaluation and checkpointing - Optimized for speed
		eval_steps=2000,  # Less frequent evaluation for speed
		save_steps=10000,  # Less frequent saving for speed
		logging_steps=50,  # More frequent logging to monitor progress
		save_total_limit=3,  # Fewer checkpoints to save disk I/O

		# Advanced options
		use_gradient_checkpointing=True,
		bf16=True,

		# NEW: Enhanced features
		use_data_augmentation=True,
		corruption_probability=0.3,
		max_corruption_ratio=0.2,
		use_dynamic_batching=False,
		min_batch_size=2,
		max_batch_size=4,
		compute_generation_metrics=True,
		compute_calibration_metrics=True,
		max_generation_samples=10,  # Reduced for speed

		# Logging
		wandb_project="diffusion-llm-enhanced",
		run_name="multi-format-training-v3"
	)

	# Initialize trainer
	trainer = DiffusionTrainer(config)

	# Start training
	trainer.train()

if __name__ == "__main__":
	main()
