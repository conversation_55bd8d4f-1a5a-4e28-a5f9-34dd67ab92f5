
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, DistributedSampler
from torch.optim import AdamW
from torch.optim.lr_scheduler import CosineAnnealingLR, LinearLR
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP

import json
import random
import logging
import os
import math
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass, field
from transformers import AutoTokenizer
import numpy as np
from tqdm import tqdm
import wandb

# Import our Diffusion LLM components
from model import (
	DiffusionLLM, DiffusionConfig, DiffusionScheduler,
	MemoryManager, ConfidenceEstimator, TokenEditor,
	ConsistencyChecker, EarlyStoppingMonitor, BranchManager
)

@dataclass
class TrainingConfig:
	"""Comprehensive training configuration"""
	# Model config
	model_config: DiffusionConfig = field(default_factory=DiffusionConfig)

	# Training hyperparameters
	learning_rate: float = 5e-5
	weight_decay: float = 0.01
	warmup_steps: int = 1000
	max_steps: int = 100000
	gradient_accumulation_steps: int = 4
	max_grad_norm: float = 1.0

	# Diffusion-specific
	diffusion_curriculum: bool = True
	start_diffusion_steps: int = 3
	end_diffusion_steps: int = 14
	curriculum_schedule: str = "linear"  # "linear", "exponential", "cosine"

	# Data config
	batch_size: int = 8
	max_length: int = 1024
	tokenizer_name: str = "gpt2"

	# Training format weights
	pretraining_weight: float = 0.4
	instruction_weight: float = 0.3
	conversation_weight: float = 0.2
	agentic_weight: float = 0.1

	# Loss weights
	generation_loss_weight: float = 1.0
	consistency_loss_weight: float = 0.1
	memory_loss_weight: float = 0.05
	confidence_loss_weight: float = 0.05

	# Evaluation
	eval_steps: int = 1000
	save_steps: int = 5000
	logging_steps: int = 100

	# Paths
	output_dir: str = "./checkpoints"
	data_dir: str = "./data"
	cache_dir: str = "./cache"

	# Distributed training
	local_rank: int = -1
	world_size: int = 1

	# Logging
	wandb_project: Optional[str] = None
	run_name: Optional[str] = None

	# Mixed precision
	fp16: bool = False
	bf16: bool = True

# ============================================================================
# TEXT FORMAT PROCESSORS
# ============================================================================

class TextFormatter:
	"""Base class for text format processing"""

	def __init__(self, tokenizer, max_length: int = 1024):
		self.tokenizer = tokenizer
		self.max_length = max_length

	def format_example(self, example: Dict[str, Any]) -> Dict[str, torch.Tensor]:
		"""Format a single example into model inputs"""
		raise NotImplementedError

	def create_diffusion_targets(self, input_ids: torch.Tensor,
							   num_steps: int) -> List[torch.Tensor]:
		"""Create intermediate diffusion targets for training"""
		# Generate progressive corruption and refinement targets
		targets = []
		seq_len = input_ids.size(-1)

		for step in range(num_steps):
			# Progressive noise injection (reverse diffusion)
			corruption_ratio = (num_steps - step) / num_steps
			num_corrupted = int(seq_len * corruption_ratio * 0.3)  # Max 30% corruption

			if num_corrupted > 0:
				corrupted_ids = input_ids.clone()

				# Randomly corrupt tokens
				corruption_positions = torch.randperm(seq_len)[:num_corrupted]
				corrupted_ids[corruption_positions] = torch.randint(
					0, self.tokenizer.vocab_size, (num_corrupted,)
				)
				targets.append(corrupted_ids)
			else:
				targets.append(input_ids.clone())

		return targets

class PretrainingFormatter(TextFormatter):
	"""Format raw text for pretraining"""

	def format_example(self, example: Dict[str, Any]) -> Dict[str, torch.Tensor]:
		text = example.get("text", "")

		# Tokenize with padding/truncation
		encoding = self.tokenizer(
			text,
			max_length=self.max_length,
			padding="max_length",
			truncation=True,
			return_tensors="pt"
		)

		input_ids = encoding["input_ids"].squeeze(0)
		attention_mask = encoding["attention_mask"].squeeze(0)

		return {
			"input_ids": input_ids,
			"attention_mask": attention_mask,
			"labels": input_ids.clone(),  # Autoregressive target
			"format_type": "pretraining"
		}

class InstructionFormatter(TextFormatter):
	"""Format instruction-following datasets"""

	def __init__(self, tokenizer, max_length: int = 1024):
		super().__init__(tokenizer, max_length)
		self.instruction_template = "### Instruction:\n{instruction}\n\n### Response:\n{response}"

	def format_example(self, example: Dict[str, Any]) -> Dict[str, torch.Tensor]:
		instruction = example.get("instruction", "")
		response = example.get("output", example.get("response", ""))

		# Add input context if available
		if "input" in example and example["input"].strip():
			instruction += f"\n\nInput: {example['input']}"

		formatted_text = self.instruction_template.format(
			instruction=instruction,
			response=response
		)

		# Tokenize full sequence
		encoding = self.tokenizer(
			formatted_text,
			max_length=self.max_length,
			padding="max_length",
			truncation=True,
			return_tensors="pt"
		)

		input_ids = encoding["input_ids"].squeeze(0)
		attention_mask = encoding["attention_mask"].squeeze(0)

		# Create labels that only compute loss on response tokens
		labels = input_ids.clone()

		# Find response start
		response_start_text = "### Response:\n"
		response_tokens = self.tokenizer.encode(response_start_text, add_special_tokens=False)

		# Mask instruction tokens (set to -100 to ignore in loss)
		response_start_idx = None
		for i in range(len(input_ids) - len(response_tokens)):
			if torch.equal(input_ids[i:i+len(response_tokens)], torch.tensor(response_tokens)):
				response_start_idx = i + len(response_tokens)
				break

		if response_start_idx is not None:
			labels[:response_start_idx] = -100

		return {
			"input_ids": input_ids,
			"attention_mask": attention_mask,
			"labels": labels,
			"format_type": "instruction"
		}

class ConversationFormatter(TextFormatter):
	"""Format multi-turn conversations"""

	def __init__(self, tokenizer, max_length: int = 1024):
		super().__init__(tokenizer, max_length)
		self.system_token = "<|system|>"
		self.user_token = "<|user|>"
		self.assistant_token = "<|assistant|>"
		self.end_token = "<|end|>"

		# Add special tokens to tokenizer if not present
		special_tokens = [self.system_token, self.user_token, self.assistant_token, self.end_token]
		self.tokenizer.add_special_tokens({"additional_special_tokens": special_tokens})

	def format_example(self, example: Dict[str, Any]) -> Dict[str, torch.Tensor]:
		conversations = example.get("conversations", [])

		# Build conversation string
		formatted_parts = []

		if "system" in example:
			formatted_parts.append(f"{self.system_token}{example['system']}{self.end_token}")

		for turn in conversations:
			role = turn.get("from", turn.get("role", ""))
			content = turn.get("value", turn.get("content", ""))

			if role in ["human", "user"]:
				formatted_parts.append(f"{self.user_token}{content}{self.end_token}")
			elif role in ["gpt", "assistant"]:
				formatted_parts.append(f"{self.assistant_token}{content}{self.end_token}")

		formatted_text = "".join(formatted_parts)

		encoding = self.tokenizer(
			formatted_text,
			max_length=self.max_length,
			padding="max_length",
			truncation=True,
			return_tensors="pt"
		)

		input_ids = encoding["input_ids"].squeeze(0)
		attention_mask = encoding["attention_mask"].squeeze(0)

		# Create labels that only compute loss on assistant responses
		labels = input_ids.clone()

		# Mask everything except assistant responses
		assistant_token_id = self.tokenizer.convert_tokens_to_ids(self.assistant_token)
		end_token_id = self.tokenizer.convert_tokens_to_ids(self.end_token)

		in_assistant_response = False
		for i, token_id in enumerate(input_ids):
			if token_id == assistant_token_id:
				in_assistant_response = True
				labels[i] = -100  # Don't compute loss on the assistant token itself
			elif token_id == end_token_id and in_assistant_response:
				in_assistant_response = False
				labels[i] = -100  # Don't compute loss on end token
			elif not in_assistant_response:
				labels[i] = -100

		return {
			"input_ids": input_ids,
			"attention_mask": attention_mask,
			"labels": labels,
			"format_type": "conversation"
		}

class AgenticFormatter(TextFormatter):
	"""Format agentic/tool-use datasets"""

	def __init__(self, tokenizer, max_length: int = 1024):
		super().__init__(tokenizer, max_length)
		self.thought_token = "<|thought|>"
		self.action_token = "<|action|>"
		self.observation_token = "<|observation|>"
		self.end_token = "<|end|>"

		# Add special tokens
		special_tokens = [self.thought_token, self.action_token, self.observation_token, self.end_token]
		self.tokenizer.add_special_tokens({"additional_special_tokens": special_tokens})

	def format_example(self, example: Dict[str, Any]) -> Dict[str, torch.Tensor]:
		# Handle different agentic formats
		if "steps" in example:
			# ReAct-style format
			formatted_parts = []

			task = example.get("task", example.get("question", ""))
			if task:
				formatted_parts.append(f"Task: {task}\n")

			for step in example["steps"]:
				if "thought" in step:
					formatted_parts.append(f"{self.thought_token}{step['thought']}{self.end_token}")
				if "action" in step:
					formatted_parts.append(f"{self.action_token}{step['action']}{self.end_token}")
				if "observation" in step:
					formatted_parts.append(f"{self.observation_token}{step['observation']}{self.end_token}")

			if "answer" in example:
				formatted_parts.append(f"Answer: {example['answer']}")

		else:
			# Simple tool use format
			task = example.get("task", "")
			actions = example.get("actions", [])

			formatted_parts = [f"Task: {task}\n"]

			for action in actions:
				formatted_parts.append(f"{self.action_token}{action}{self.end_token}")

		formatted_text = "".join(formatted_parts)

		encoding = self.tokenizer(
			formatted_text,
			max_length=self.max_length,
			padding="max_length",
			truncation=True,
			return_tensors="pt"
		)

		input_ids = encoding["input_ids"].squeeze(0)
		attention_mask = encoding["attention_mask"].squeeze(0)

		# For agentic tasks, compute loss on thoughts and actions
		labels = input_ids.clone()

		# Mask observations (external feedback)
		obs_token_id = self.tokenizer.convert_tokens_to_ids(self.observation_token)
		end_token_id = self.tokenizer.convert_tokens_to_ids(self.end_token)

		in_observation = False
		for i, token_id in enumerate(input_ids):
			if token_id == obs_token_id:
				in_observation = True
				labels[i] = -100
			elif token_id == end_token_id and in_observation:
				in_observation = False
				labels[i] = -100
			elif in_observation:
				labels[i] = -100

		return {
			"input_ids": input_ids,
			"attention_mask": attention_mask,
			"labels": labels,
			"format_type": "agentic"
		}

# ============================================================================
# DATASET CLASSES
# ============================================================================

class MultiFormatDataset(Dataset):
	"""Dataset that handles multiple text formats"""

	def __init__(self, config: TrainingConfig, split: str = "train"):
		self.config = config
		self.split = split

		# Initialize tokenizer
		self.tokenizer = AutoTokenizer.from_pretrained(config.tokenizer_name)
		if self.tokenizer.pad_token is None:
			self.tokenizer.pad_token = self.tokenizer.eos_token

		# Initialize formatters
		self.formatters = {
			"pretraining": PretrainingFormatter(self.tokenizer, config.max_length),
			"instruction": InstructionFormatter(self.tokenizer, config.max_length),
			"conversation": ConversationFormatter(self.tokenizer, config.max_length),
			"agentic": AgenticFormatter(self.tokenizer, config.max_length)
		}

		# Load datasets
		self.datasets = self._load_datasets()
		self.format_weights = self._get_format_weights()

		# Create sampling weights
		self.examples = self._prepare_examples()

	def _load_datasets(self) -> Dict[str, List[Dict]]:
		"""Load datasets for each format"""
		datasets = {}
		data_dir = Path(self.config.data_dir)

		# Load pretraining data
		pretraining_file = data_dir / f"pretraining_{self.split}.jsonl"
		if pretraining_file.exists():
			datasets["pretraining"] = self._load_jsonl(pretraining_file)

		# Load instruction data
		instruction_file = data_dir / f"instruction_{self.split}.jsonl"
		if instruction_file.exists():
			datasets["instruction"] = self._load_jsonl(instruction_file)

		# Load conversation data
		conversation_file = data_dir / f"conversation_{self.split}.jsonl"
		if conversation_file.exists():
			datasets["conversation"] = self._load_jsonl(conversation_file)

		# Load agentic data
		agentic_file = data_dir / f"agentic_{self.split}.jsonl"
		if agentic_file.exists():
			datasets["agentic"] = self._load_jsonl(agentic_file)

		return datasets

	def _load_jsonl(self, file_path: Path) -> List[Dict]:
		"""Load JSONL file"""
		data = []
		with open(file_path, 'r', encoding='utf-8') as f:
			for line in f:
				data.append(json.loads(line.strip()))
		return data

	def _get_format_weights(self) -> Dict[str, float]:
		"""Get sampling weights for each format"""
		return {
			"pretraining": self.config.pretraining_weight,
			"instruction": self.config.instruction_weight,
			"conversation": self.config.conversation_weight,
			"agentic": self.config.agentic_weight
		}

	def _prepare_examples(self) -> List[Tuple[str, int]]:
		"""Prepare weighted list of (format, index) pairs"""
		examples = []

		for format_name, dataset in self.datasets.items():
			weight = self.format_weights.get(format_name, 0.0)
			if weight > 0 and len(dataset) > 0:
				# Calculate number of samples based on weight
				num_samples = int(len(dataset) * weight * 10)  # Scale up for better distribution

				for _ in range(num_samples):
					idx = random.randint(0, len(dataset) - 1)
					examples.append((format_name, idx))

		random.shuffle(examples)
		return examples

	def __len__(self) -> int:
		return len(self.examples)

	def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
		format_name, data_idx = self.examples[idx]

		# Get raw example
		raw_example = self.datasets[format_name][data_idx]

		# Format using appropriate formatter
		formatter = self.formatters[format_name]
		formatted_example = formatter.format_example(raw_example)

		return formatted_example

# ============================================================================
# LOSS FUNCTIONS
# ============================================================================

class DiffusionLoss(nn.Module):
	"""Multi-component loss for diffusion training"""

	def __init__(self, config: TrainingConfig):
		super().__init__()
		self.config = config

		# Loss weights
		self.generation_weight = config.generation_loss_weight
		self.consistency_weight = config.consistency_loss_weight
		self.memory_weight = config.memory_loss_weight
		self.confidence_weight = config.confidence_loss_weight

	def forward(self, outputs: Dict[str, torch.Tensor],
				targets: Dict[str, torch.Tensor],
				step_outputs: List[Dict[str, torch.Tensor]],
				diffusion_step: int) -> Dict[str, torch.Tensor]:
		"""Compute total loss with all components"""

		losses = {}

		# 1. Generation loss (cross-entropy)
		logits = outputs["logits"]
		labels = targets["labels"]

		generation_loss = F.cross_entropy(
			logits.view(-1, logits.size(-1)),
			labels.view(-1),
			ignore_index=-100,
			reduction='mean'
		)
		losses["generation"] = generation_loss

		# 2. Diffusion step losses (intermediate supervision)
		if len(step_outputs) > 1:
			step_losses = []
			for i, step_output in enumerate(step_outputs[:-1]):  # Exclude final step
				step_logits = step_output["logits"]
				step_loss = F.cross_entropy(
					step_logits.view(-1, step_logits.size(-1)),
					labels.view(-1),
					ignore_index=-100,
					reduction='mean'
				)
				# Weight earlier steps less
				weight = (i + 1) / len(step_outputs)
				step_losses.append(weight * step_loss)

			if step_losses:
				losses["diffusion_steps"] = torch.stack(step_losses).mean()

		# 3. Consistency loss between consecutive steps
		if len(step_outputs) > 1 and self.consistency_weight > 0:
			consistency_losses = []
			for i in range(len(step_outputs) - 1):
				prev_hidden = step_outputs[i]["hidden_states"]
				curr_hidden = step_outputs[i + 1]["hidden_states"]

				# Cosine similarity loss
				prev_norm = F.normalize(prev_hidden, dim=-1)
				curr_norm = F.normalize(curr_hidden, dim=-1)
				similarity = torch.sum(prev_norm * curr_norm, dim=-1)

				# Encourage high similarity (smooth transitions)
				consistency_loss = 1.0 - similarity.mean()
				consistency_losses.append(consistency_loss)

			if consistency_losses:
				losses["consistency"] = torch.stack(consistency_losses).mean()

		# 4. Confidence calibration loss
		if "confidence_scores" in outputs and self.confidence_weight > 0:
			confidence_scores = outputs["confidence_scores"]

			# Get prediction correctness
			predictions = torch.argmax(logits, dim=-1)
			correct_mask = (predictions == labels) & (labels != -100)

			# Confidence should be high for correct predictions
			confidence_target = correct_mask.float()
			confidence_loss = F.binary_cross_entropy(
				confidence_scores.view(-1),
				confidence_target.view(-1),
				reduction='mean'
			)
			losses["confidence"] = confidence_loss

		# 5. Memory reconstruction loss (if applicable)
		if "memory_reconstruction" in outputs and self.memory_weight > 0:
			memory_recon = outputs["memory_reconstruction"]
			memory_target = outputs.get("memory_target", targets["labels"])

			memory_loss = F.mse_loss(memory_recon, memory_target.float())
			losses["memory"] = memory_loss

		# Compute weighted total loss
		total_loss = self.generation_weight * losses["generation"]

		if "diffusion_steps" in losses:
			total_loss += 0.5 * losses["diffusion_steps"]

		if "consistency" in losses:
			total_loss += self.consistency_weight * losses["consistency"]

		if "confidence" in losses:
			total_loss += self.confidence_weight * losses["confidence"]

		if "memory" in losses:
			total_loss += self.memory_weight * losses["memory"]

		losses["total"] = total_loss

		return losses

# ============================================================================
# CURRICULUM LEARNING
# ============================================================================

class DiffusionCurriculum:
	"""Manages curriculum learning for diffusion complexity"""

	def __init__(self, config: TrainingConfig):
		self.config = config
		self.start_steps = config.start_diffusion_steps
		self.end_steps = config.end_diffusion_steps
		self.schedule = config.curriculum_schedule

	def get_diffusion_steps(self, current_step: int, max_steps: int) -> int:
		"""Get number of diffusion steps based on training progress"""
		if not self.config.diffusion_curriculum:
			return self.end_steps

		progress = min(current_step / max_steps, 1.0)

		if self.schedule == "linear":
			steps = self.start_steps + progress * (self.end_steps - self.start_steps)
		elif self.schedule == "exponential":
			# Slow start, rapid increase
			exp_progress = progress ** 2
			steps = self.start_steps + exp_progress * (self.end_steps - self.start_steps)
		elif self.schedule == "cosine":
			# Smooth S-curve
			cos_progress = 0.5 * (1 - math.cos(math.pi * progress))
			steps = self.start_steps + cos_progress * (self.end_steps - self.start_steps)
		else:
			steps = self.end_steps

		return max(1, min(int(steps), self.end_steps))

# ============================================================================
# TRAINING UTILITIES
# ============================================================================

def setup_logging(config: TrainingConfig) -> logging.Logger:
	"""Setup logging configuration"""
	logging.basicConfig(
		format='%(asctime)s - %(levelname)s - %(message)s',
		level=logging.INFO
	)
	logger = logging.getLogger(__name__)

	if config.wandb_project and config.local_rank <= 0:
		wandb.init(
			project=config.wandb_project,
			name=config.run_name,
			config=config.__dict__
		)

	return logger

def setup_distributed_training(config: TrainingConfig) -> None:
	"""Setup distributed training"""
	if config.local_rank != -1:
		torch.cuda.set_device(config.local_rank)
		dist.init_process_group(backend='nccl')
		config.world_size = dist.get_world_size()

def save_checkpoint(model: nn.Module, optimizer: torch.optim.Optimizer,
				   scheduler: Any, step: int, config: TrainingConfig,
				   losses: Dict[str, float]) -> None:
	"""Save training checkpoint"""
	if config.local_rank <= 0:
		checkpoint = {
			'model_state_dict': model.state_dict(),
			'optimizer_state_dict': optimizer.state_dict(),
			'scheduler_state_dict': scheduler.state_dict(),
			'step': step,
			'config': config,
			'losses': losses
		}

		save_path = Path(config.output_dir) / f"checkpoint-{step}.pt"
		save_path.parent.mkdir(parents=True, exist_ok=True)
		torch.save(checkpoint, save_path)

def load_checkpoint(model: nn.Module, optimizer: torch.optim.Optimizer,
				   scheduler: Any, checkpoint_path: str) -> int:
	"""Load training checkpoint"""
	checkpoint = torch.load(checkpoint_path, map_location='cpu')

	model.load_state_dict(checkpoint['model_state_dict'])
	optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
	scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

	return checkpoint['step']

# ============================================================================
# MAIN TRAINER CLASS
# ============================================================================

class DiffusionTrainer:
	"""Main training class for Diffusion LLM"""

	def __init__(self, config: TrainingConfig):
		self.config = config
		self.logger = setup_logging(config)

		# Setup distributed training
		setup_distributed_training(config)

		# Initialize model components
		self._init_model()
		self._init_optimizer()
		self._init_data()
		self._init_training_components()

	def _init_model(self) -> None:
		"""Initialize model and move to device"""
		self.device = torch.device(f"cuda:{self.config.local_rank}" if torch.cuda.is_available() else "cpu")

		# Create model
		self.model = DiffusionLLM(self.config.model_config)
		self.model = self.model.to(self.device)

		# Wrap with DDP if distributed
		if self.config.local_rank != -1:
			self.model = DDP(self.model, device_ids=[self.config.local_rank])

		# Mixed precision scaler
		self.scaler = torch.cuda.amp.GradScaler(enabled=self.config.fp16)

	def _init_optimizer(self) -> None:
		"""Initialize optimizer and scheduler"""
		# Separate learning rates for different components
		param_groups = [
			{
				'params': [p for n, p in self.model.named_parameters() if 'memory' not in n],
				'lr': self.config.learning_rate,
				'weight_decay': self.config.weight_decay
			},
			{
				'params': [p for n, p in self.model.named_parameters() if 'memory' in n],
				'lr': self.config.learning_rate * 0.5,  # Lower LR for memory components
				'weight_decay': self.config.weight_decay * 0.5
			}
		]

		self.optimizer = AdamW(param_groups, eps=1e-8)

		# Learning rate scheduler
		self.scheduler = CosineAnnealingLR(
			self.optimizer,
			T_max=self.config.max_steps,
			eta_min=self.config.learning_rate * 0.1
		)

		# Warmup scheduler
		self.warmup_scheduler = LinearLR(
			self.optimizer,
			start_factor=0.1,
			total_iters=self.config.warmup_steps
		)

	def _init_data(self) -> None:
		"""Initialize datasets and data loaders"""
		# Training dataset
		self.train_dataset = MultiFormatDataset(self.config, split="train")

		# Distributed sampler
		train_sampler = None
		if self.config.local_rank != -1:
			train_sampler = DistributedSampler(
				self.train_dataset,
				num_replicas=self.config.world_size,
				rank=self.config.local_rank
			)

		self.train_loader = DataLoader(
			self.train_dataset,
			batch_size=self.config.batch_size,
			sampler=train_sampler,
			shuffle=(train_sampler is None),
			num_workers=4,
			pin_memory=True
		)

		# Validation dataset
		try:
			self.val_dataset = MultiFormatDataset(self.config, split="validation")
			self.val_loader = DataLoader(
				self.val_dataset,
				batch_size=self.config.batch_size,
				shuffle=False,
				num_workers=2,
				pin_memory=True
			)
		except:
			self.val_loader = None
			self.logger.warning("Validation dataset not found")

	def _init_training_components(self) -> None:
		"""Initialize training-specific components"""
		self.loss_fn = DiffusionLoss(self.config)
		self.curriculum = DiffusionCurriculum(self.config)

		# Training state
		self.global_step = 0
		self.best_val_loss = float('inf')

	def train_step(self, batch: Dict[str, torch.Tensor]) -> Dict[str, float]:
		"""Execute single training step"""
		self.model.train()

		# Move batch to device
		batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}

		# Get current diffusion steps from curriculum
		diffusion_steps = self.curriculum.get_diffusion_steps(self.global_step, self.config.max_steps)

		# Mixed precision training context
		precision_context = torch.cuda.amp.autocast(enabled=self.config.fp16 or self.config.bf16, dtype=torch.bfloat16 if self.config.bf16 else torch.float16)

		with precision_context:
			# Forward pass through diffusion process
			step_outputs = []
			current_sequence = batch["input_ids"]

			for step in range(diffusion_steps):
				# Run diffusion step
				outputs = self.model(
					input_ids=current_sequence,
					attention_mask=batch["attention_mask"],
					step=step,
					max_steps=diffusion_steps
				)

				step_outputs.append(outputs)

				# Update sequence for next step (simplified)
				if step < diffusion_steps - 1:
					# Apply token edits based on confidence
					confidence_scores = outputs.get("confidence_scores")
					if confidence_scores is not None:
						# Select tokens to edit
						edit_count = max(1, int(current_sequence.size(1) * 0.1))
						_, edit_positions = torch.topk(confidence_scores, edit_count, dim=-1, largest=False)

						# Generate new tokens (simplified)
						logits = outputs["logits"]
						new_tokens = torch.multinomial(F.softmax(logits.view(-1, logits.size(-1)), dim=-1), 1)
						new_tokens = new_tokens.view(current_sequence.shape)

						# Replace tokens at edit positions
						for b in range(current_sequence.size(0)):
							for pos in edit_positions[b]:
								if pos < current_sequence.size(1):
									current_sequence[b, pos] = new_tokens[b, pos]

			# Compute losses
			final_outputs = step_outputs[-1]
			losses = self.loss_fn(final_outputs, batch, step_outputs, diffusion_steps)

		# Backward pass
		self.scaler.scale(losses["total"]).backward()

		# Gradient accumulation
		if (self.global_step + 1) % self.config.gradient_accumulation_steps == 0:
			# Gradient clipping
			self.scaler.unscale_(self.optimizer)
			torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.max_grad_norm)

			# Optimizer step
			self.scaler.step(self.optimizer)
			self.scaler.update()

			# Learning rate scheduling
			if self.global_step < self.config.warmup_steps:
				self.warmup_scheduler.step()
			else:
				self.scheduler.step()

			self.optimizer.zero_grad()

		return {k: v.item() if torch.is_tensor(v) else v for k, v in losses.items()}

	@torch.no_grad()
	def evaluate(self) -> Dict[str, float]:
		"""Evaluate model on validation set"""
		if self.val_loader is None:
			return {}

		self.model.eval()

		total_losses = {}
		num_batches = 0

		for batch in self.val_loader:
			batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v for k, v in batch.items()}

			# Simple forward pass for evaluation
			outputs = self.model(
				input_ids=batch["input_ids"],
				attention_mask=batch["attention_mask"]
			)

			# Compute losses
			losses = self.loss_fn(outputs, batch, [outputs], 1)

			# Accumulate losses
			for k, v in losses.items():
				if k not in total_losses:
					total_losses[k] = 0.0
				total_losses[k] += v.item()

			num_batches += 1

			if num_batches >= 100:  # Limit evaluation time
				break

		# Average losses
		avg_losses = {f"val_{k}": v / num_batches for k, v in total_losses.items()}

		return avg_losses

	def train(self) -> None:
		"""Main training loop"""
		self.logger.info("Starting training...")

		data_iter = iter(self.train_loader)
		moving_avg_loss = None

		for step in range(self.config.max_steps):
			self.global_step = step

			try:
				batch = next(data_iter)
			except StopIteration:
				data_iter = iter(self.train_loader)
				batch = next(data_iter)

			# Training step
			losses = self.train_step(batch)

			# Moving average loss
			if moving_avg_loss is None:
				moving_avg_loss = losses["total"]
			else:
				moving_avg_loss = 0.9 * moving_avg_loss + 0.1 * losses["total"]

			# Logging
			if step % self.config.logging_steps == 0:
				lr = self.optimizer.param_groups[0]['lr']
				diffusion_steps = self.curriculum.get_diffusion_steps(step, self.config.max_steps)

				log_dict = {
					"step": step,
					"learning_rate": lr,
					"diffusion_steps": diffusion_steps,
					"moving_avg_loss": moving_avg_loss,
					**losses
				}

				self.logger.info(f"Step {step}: Loss = {moving_avg_loss:.4f}, LR = {lr:.2e}, Diff Steps = {diffusion_steps}")

				if self.config.wandb_project and self.config.local_rank <= 0:
					wandb.log(log_dict)

			# Evaluation
			if step > 0 and step % self.config.eval_steps == 0:
				val_losses = self.evaluate()

				if val_losses and self.config.local_rank <= 0:
					self.logger.info(f"Validation - Step {step}: {val_losses}")

					if self.config.wandb_project:
						wandb.log(val_losses)

					# Save best model
					val_loss = val_losses.get("val_total", float('inf'))
					if val_loss < self.best_val_loss:
						self.best_val_loss = val_loss
						save_checkpoint(self.model, self.optimizer, self.scheduler, step, self.config, val_losses)

			# Regular checkpointing
			if step > 0 and step % self.config.save_steps == 0:
				save_checkpoint(self.model, self.optimizer, self.scheduler, step, self.config, losses)

		self.logger.info("Training completed!")

# ============================================================================
# TRAINING SCRIPT
# ============================================================================

def main():
	"""Main training function"""
	# Configuration
	config = TrainingConfig(
		# Model config
		model_config=DiffusionConfig(
			vocab_size=50257,
			hidden_dim=768,
			num_layers=12,
			num_heads=12,
			max_length=1024,
			diffusion_steps=14
		),

		# Training config
		learning_rate=5e-5,
		batch_size=8,
		max_steps=100000,
		warmup_steps=1000,

		# Data config
		data_dir="./data",
		output_dir="./checkpoints",

		# Format weights
		pretraining_weight=0.4,
		instruction_weight=0.3,
		conversation_weight=0.2,
		agentic_weight=0.1,

		# Logging
		wandb_project="diffusion-llm",
		run_name="multi-format-training"
	)

	# Initialize trainer
	trainer = DiffusionTrainer(config)

	# Start training
	trainer.train()

if __name__ == "__main__":
	main()
