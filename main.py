
"""
Unified Training System for Diffusion LLM

This main.py systematically organizes all training modes into a coherent system:

1. **Pretraining Mode**: Large-scale unsupervised learning on datasets like WikiText
2. **Distillation Mode**: Parent-child training using DeepSeek-R1 as teacher
3. **Multi-Format Mode**: Mixed training on instructions, conversations, and agentic tasks

Each mode builds upon the previous, creating a complete training pipeline from
raw text to sophisticated reasoning capabilities.
"""


from dataclasses import asdict
from pathlib import Path
from typing import Optional, Dict, Any

import argparse
import asyncio
import json
import logging
import sys
import time

# Import our training components
from model import DiffusionConfig
from train import TrainingConfig, DiffusionTrainer
from pretraining import PretrainingDatasetConfig, PretrainingDatasetFactory
from distill_train import (
	ParentChildTrainingConfig, ParentChildTrainer,
	ParentModelConfig, DataGenerationConfig
)

# ============================================================================
# FUNDAMENTAL COMPONENT 1: UNIFIED CONFIGURATION SYSTEM
# ============================================================================

class TrainingMode:
	"""Enumeration of available training modes with educational descriptions."""

	PRETRAINING = "pretraining"
	DISTILLATION = "distillation"
	MULTIFORMAT = "multiformat"

	@classmethod
	def get_descriptions(cls) -> Dict[str, str]:
		"""Get educational descriptions of each training mode."""
		return {
			cls.PRETRAINING: (
				"Large-scale unsupervised learning on clean datasets like WikiText. "
				"Builds fundamental language understanding and establishes base model capabilities."
			),
			cls.DISTILLATION: (
				"Parent-child knowledge transfer using DeepSeek-R1 as teacher model. "
				"Transfers reasoning patterns and thought processes from advanced models."
			),
			cls.MULTIFORMAT: (
				"Multi-task training on instructions, conversations, and agentic tasks. "
				"Develops specialized capabilities for different interaction patterns."
			)
		}

class CheckpointManager:
	"""
	Systematic checkpoint management across all training modes.

	This manager provides consistent checkpoint handling, automatic backup,
	and cross-mode compatibility for seamless training progression.
	"""

	def __init__(self, base_dir: str = "./checkpoints"):
		self.base_dir = Path(base_dir)
		self.base_dir.mkdir(parents=True, exist_ok=True)
		self.logger = logging.getLogger(f"{__name__}.CheckpointManager")

	def get_checkpoint_dir(self, mode: str, run_name: str) -> Path:
		"""Get organized checkpoint directory for specific training mode."""
		checkpoint_dir = self.base_dir / mode / run_name
		checkpoint_dir.mkdir(parents=True, exist_ok=True)
		return checkpoint_dir

	def find_latest_checkpoint(self, mode: str, run_name: str) -> Optional[Path]:
		"""Find the most recent checkpoint for resuming training."""
		checkpoint_dir = self.get_checkpoint_dir(mode, run_name)

		# Look for checkpoint files
		checkpoints = list(checkpoint_dir.glob("checkpoint-*.pt"))
		if not checkpoints:
			return None

		# Sort by step number and return latest
		checkpoints.sort(key=lambda x: int(x.stem.split('-')[1]))
		latest = checkpoints[-1]

		self.logger.info(f"Found latest checkpoint: {latest}")
		return latest

	def save_training_config(self, mode: str, run_name: str, config: Any) -> None:
		"""Save training configuration for reproducibility."""
		checkpoint_dir = self.get_checkpoint_dir(mode, run_name)
		config_path = checkpoint_dir / "training_config.json"

		# Convert dataclass to dict if needed
		if hasattr(config, '__dataclass_fields__'):
			config_dict = asdict(config)
		else:
			config_dict = config.__dict__ if hasattr(config, '__dict__') else str(config)

		with open(config_path, 'w') as f:
			json.dump(config_dict, f, indent=2, default=str)

		self.logger.info(f"Saved training config to {config_path}")

	def load_training_config(self, mode: str, run_name: str) -> Optional[Dict[str, Any]]:
		"""Load previously saved training configuration."""
		checkpoint_dir = self.get_checkpoint_dir(mode, run_name)
		config_path = checkpoint_dir / "training_config.json"

		if not config_path.exists():
			return None

		with open(config_path, 'r') as f:
			config_dict = json.load(f)

		self.logger.info(f"Loaded training config from {config_path}")
		return config_dict

def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None) -> logging.Logger:
	"""Setup comprehensive logging for all training modes."""

	# Configure root logger
	handlers = [logging.StreamHandler(sys.stdout)]

	if log_file:
		log_path = Path(log_file)
		log_path.parent.mkdir(parents=True, exist_ok=True)
		handlers.append(logging.FileHandler(log_path))

	logging.basicConfig(
		level=getattr(logging, log_level.upper()),
		format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
		handlers=handlers
	)

	logger = logging.getLogger(__name__)
	logger.info("Logging system initialized")
	return logger

# ============================================================================
# FUNDAMENTAL COMPONENT 2: PRETRAINING FUNCTION
# ============================================================================

def run_pretraining(
	dataset_name: str = "wikitext",
	dataset_variant: str = "wikitext-103-raw-v1",
	resume_from: Optional[str] = None,
	run_name: str = "pretraining_run",
	max_steps: int = 50000,
	batch_size: int = 8,
	learning_rate: float = 5e-5,
	use_wandb: bool = True
) -> None:
	"""
	Execute systematic pretraining on large text corpora.

	Pretraining represents the foundational phase of language model development,
	where the model learns basic language patterns, syntax, and world knowledge
	from large amounts of unlabeled text data.

	Educational Framework:
	----------------------
	1. **Data Foundation**: Clean, high-quality text provides robust learning signal
	2. **Scale Benefits**: Large datasets teach diverse language patterns
	3. **Unsupervised Learning**: No labels needed - predict next token from context
	4. **Transfer Learning**: Pretrained weights transfer to downstream tasks

	Args:
		dataset_name: Dataset to use (wikitext, openwebtext, pile, etc.)
		dataset_variant: Specific variant within dataset
		resume_from: Path to checkpoint for resuming training
		run_name: Identifier for this training run
		max_steps: Maximum training steps
		batch_size: Training batch size
		learning_rate: Learning rate for optimization
		use_wandb: Whether to use Weights & Biases logging
	"""

	logger = logging.getLogger(f"{__name__}.pretraining")
	logger.info("=" * 80)
	logger.info("STARTING PRETRAINING MODE")
	logger.info("=" * 80)

	# Step 1: Initialize checkpoint management
	checkpoint_manager = CheckpointManager("./checkpoints")
	checkpoint_dir = checkpoint_manager.get_checkpoint_dir("pretraining", run_name)

	logger.info(f"Pretraining run: {run_name}")
	logger.info(f"Dataset: {dataset_name}/{dataset_variant}")
	logger.info(f"Checkpoint directory: {checkpoint_dir}")

	try:
		# Step 2: Configure pretraining dataset creation
		dataset_config = PretrainingDatasetConfig(
			# Dataset selection
			dataset_name=dataset_name,
			dataset_variant=dataset_variant,

			# Processing parameters optimized for pretraining
			max_length=1024,
			min_length=50,
			overlap_length=128,
			chunk_strategy="sliding_window",  # Optimal for continuous text

			# Tokenizer settings (must match model)
			tokenizer_name="gpt2",

			# Quality filtering for clean pretraining data
			min_words_per_chunk=20,
			max_words_per_chunk=500,
			filter_repetitive_text=True,
			min_unique_words_ratio=0.3,
			filter_non_english=True,
			min_alpha_ratio=0.7,

			# Data organization
			validation_split=0.01,
			shuffle_data=True,
			deduplicate=True,

			# Performance optimization
			num_workers=4,
			use_multiprocessing=True,

			# Directory configuration
			cache_dir="./data_cache",
			download_dir="./downloads",
			processed_dir="./processed_data"
		)

		# Step 3: Create pretraining datasets
		logger.info("Creating pretraining datasets...")
		dataset_files = PretrainingDatasetFactory.create_dataset_files(
			dataset_config,
			output_dir="./data"
		)
		logger.info(f"Created dataset files: {dataset_files}")

		# Step 4: Configure model for pretraining
		model_config = DiffusionConfig(
			# Model architecture
			vocab_size=50257,  # GPT-2 vocabulary
			hidden_dim=768,
			num_layers=12,
			num_heads=12,
			max_length=1024,

			# Diffusion settings optimized for pretraining
			diffusion_steps=8,  # Fewer steps for pretraining efficiency
			use_kv_cache=True,
			confidence_calibration=True,

			# Memory configuration
			st_memory_size=100,
			mt_memory_size=500,
			lt_memory_size=2000,

			# Stability settings
			dropout=0.1,
			max_grad_norm=1.0
		)

		# Step 5: Configure training parameters
		training_config = TrainingConfig(
			# Model configuration
			model_config=model_config,

			# Training hyperparameters optimized for pretraining
			learning_rate=learning_rate,
			weight_decay=0.01,
			warmup_steps=2000,  # Longer warmup for stability
			max_steps=max_steps,
			gradient_accumulation_steps=4,
			max_grad_norm=1.0,

			# Diffusion curriculum for progressive complexity
			diffusion_curriculum=True,
			start_diffusion_steps=2,  # Start simple
			end_diffusion_steps=8,    # Build to moderate complexity
			curriculum_schedule="cosine",

			# Data configuration - pure pretraining focus
			batch_size=batch_size,
			max_length=1024,
			tokenizer_name="gpt2",

			# Format weights - 100% pretraining
			pretraining_weight=1.0,
			instruction_weight=0.0,
			conversation_weight=0.0,
			agentic_weight=0.0,

			# Loss weights optimized for language modeling
			generation_loss_weight=1.0,
			consistency_loss_weight=0.05,  # Light consistency for stability
			memory_loss_weight=0.02,       # Light memory supervision
			confidence_loss_weight=0.02,   # Light confidence calibration

			# Evaluation and checkpointing
			eval_steps=2000,
			save_steps=5000,
			logging_steps=100,
			save_total_limit=5,

			# Paths
			output_dir=str(checkpoint_dir),
			data_dir="./data",
			cache_dir="./cache",
			resume_from_checkpoint=resume_from,

			# Distributed training
			local_rank=-1,
			world_size=1,

			# Logging
			wandb_project="diffusion-llm-pretraining" if use_wandb else None,
			run_name=run_name,

			# Performance optimization
			bf16=True,
			use_gradient_checkpointing=False,  # Disable for pretraining speed
			dataloader_num_workers=4,

			# Pretraining-specific optimization
			use_data_augmentation=False,  # Clean data doesn't need augmentation
			use_dynamic_batching=True,
			min_batch_size=4,
			max_batch_size=16,

			# Evaluation settings
			compute_generation_metrics=True,
			compute_calibration_metrics=True,
			max_generation_samples=50
		)

		# Step 6: Save configuration for reproducibility
		checkpoint_manager.save_training_config("pretraining", run_name, training_config)

		# Step 7: Initialize and run training
		logger.info("Initializing pretraining...")
		trainer = DiffusionTrainer(training_config)

		logger.info("Starting pretraining loop...")
		trainer.train()

		logger.info("Pretraining completed successfully!")

		# Step 8: Save final summary
		final_checkpoint = checkpoint_manager.find_latest_checkpoint("pretraining", run_name)
		logger.info(f"Final checkpoint saved: {final_checkpoint}")

	except Exception as e:
		logger.error(f"Pretraining failed: {str(e)}")
		raise

# ============================================================================
# FUNDAMENTAL COMPONENT 3: DISTILLATION TRAINING FUNCTION
# ============================================================================

async def run_distillation_training(
	parent_model: str = "DeepSeek-R1-0528-Qwen3-8B-BF16:latest",
	ollama_host: str = "http://localhost:11434",
	pretrained_checkpoint: Optional[str] = None,
	resume_from: Optional[str] = None,
	run_name: str = "distillation_run",
	max_steps: int = 25000,
	batch_size: int = 4,
	learning_rate: float = 3e-5,
	use_wandb: bool = True
) -> None:
	"""
	Execute systematic knowledge distillation from advanced parent models.

	Distillation training represents the knowledge transfer phase, where our
	diffusion model learns sophisticated reasoning patterns from a more
	advanced teacher model like DeepSeek-R1.

	Educational Framework:
	----------------------
	1. **Knowledge Transfer**: Copy capabilities from advanced models
	2. **Reasoning Patterns**: Learn structured thinking processes
	3. **Thought Alignment**: Match internal reasoning representations
	4. **Progressive Learning**: Start with generation, add reasoning gradually

	Args:
		parent_model: Ollama model name for parent/teacher
		ollama_host: Ollama server host URL
		pretrained_checkpoint: Path to pretrained model checkpoint
		resume_from: Path to checkpoint for resuming distillation
		run_name: Identifier for this training run
		max_steps: Maximum training steps
		batch_size: Training batch size
		learning_rate: Learning rate for optimization
		use_wandb: Whether to use Weights & Biases logging
	"""

	logger = logging.getLogger(f"{__name__}.distillation")
	logger.info("=" * 80)
	logger.info("STARTING DISTILLATION TRAINING MODE")
	logger.info("=" * 80)

	# Step 1: Initialize checkpoint management
	checkpoint_manager = CheckpointManager("./checkpoints")
	checkpoint_dir = checkpoint_manager.get_checkpoint_dir("distillation", run_name)

	logger.info(f"Distillation run: {run_name}")
	logger.info(f"Parent model: {parent_model}")
	logger.info(f"Ollama host: {ollama_host}")
	logger.info(f"Checkpoint directory: {checkpoint_dir}")

	try:
		# Step 2: Configure parent model connection
		parent_config = ParentModelConfig(
			model_name=parent_model,
			ollama_host=ollama_host,
			max_tokens=2048,
			temperature=0.7,
			concurrent_requests=4,
			max_retries=3,
			timeout=120
		)

		# Step 3: Configure data generation from parent
		data_generation_config = DataGenerationConfig(
			# Balanced task distribution for reasoning development
			num_examples_per_category=1000,
			max_concurrent_generations=4,
			output_dir="./generated_distillation_data",

			# Task weights emphasizing reasoning
			reasoning_weight=0.4,    # Heavy focus on reasoning
			factual_weight=0.2,     # Factual knowledge
			creative_weight=0.2,    # Creative thinking
			instruction_weight=0.15, # Following instructions
			conversation_weight=0.05, # Basic conversation

			# Quality control for high-value examples
			min_thought_length=50,
			min_output_length=20,
			max_output_length=1000,
			save_interval=100
		)

		# Step 4: Configure child model (builds on pretraining)
		model_config = DiffusionConfig(
			# Model architecture (can be loaded from pretrained)
			vocab_size=50257,
			hidden_dim=768,
			num_layers=12,
			num_heads=12,
			max_length=1024,

			# Enhanced diffusion for reasoning
			diffusion_steps=12,  # More steps for complex reasoning
			use_kv_cache=True,
			confidence_calibration=True,

			# Enhanced memory for thought processes
			st_memory_size=150,
			mt_memory_size=750,
			lt_memory_size=3000,

			# Stability for distillation
			dropout=0.1,
			max_grad_norm=1.0
		)

		# Step 5: Configure distillation training
		child_training_config = TrainingConfig(
			# Model configuration
			model_config=model_config,

			# Training hyperparameters for distillation
			learning_rate=learning_rate,
			weight_decay=0.01,
			warmup_steps=1000,
			max_steps=max_steps,
			gradient_accumulation_steps=4,
			max_grad_norm=1.0,

			# Progressive diffusion curriculum
			diffusion_curriculum=True,
			start_diffusion_steps=3,
			end_diffusion_steps=12,
			curriculum_schedule="cosine",

			# Data configuration
			batch_size=batch_size,
			max_length=1024,
			tokenizer_name="gpt2",

			# Balanced format weights for diverse reasoning
			pretraining_weight=0.3,   # Maintain language foundation
			instruction_weight=0.3,   # Instruction following
			conversation_weight=0.2,  # Conversational reasoning
			agentic_weight=0.2,       # Complex reasoning tasks

			# Loss weights optimized for distillation
			generation_loss_weight=1.0,
			consistency_loss_weight=0.1,
			memory_loss_weight=0.05,
			confidence_loss_weight=0.05,

			# Evaluation and checkpointing
			eval_steps=1000,
			save_steps=2500,
			logging_steps=50,
			save_total_limit=5,

			# Paths
			output_dir=str(checkpoint_dir),
			data_dir="./data",
			cache_dir="./cache",
			resume_from_checkpoint=resume_from,

			# Logging
			wandb_project="diffusion-llm-distillation" if use_wandb else None,
			run_name=run_name,

			# Performance settings
			bf16=True,
			dataloader_num_workers=4
		)

		# Step 6: Configure parent-child system
		distillation_config = ParentChildTrainingConfig(
			# Component configurations
			parent_config=parent_config,
			data_generation_config=data_generation_config,
			child_training_config=child_training_config,

			# Knowledge distillation parameters
			distillation_alpha=0.6,     # Focus on generation quality
			distillation_beta=0.25,     # Moderate thought alignment
			distillation_gamma=0.15,    # Light output alignment
			distillation_temperature=3.0,

			# Training curriculum for progressive learning
			use_curriculum=True,
			warmup_steps=1000,          # Pure generation first
			distillation_start_step=2000, # Begin knowledge transfer
			regenerate_data_interval=10000, # Refresh data periodically
			keep_old_data_ratio=0.3
		)

		# Step 7: Load pretrained checkpoint if provided
		if pretrained_checkpoint:
			logger.info(f"Loading pretrained model from: {pretrained_checkpoint}")
			# The trainer will handle loading the pretrained weights
			child_training_config.resume_from_checkpoint = pretrained_checkpoint

		# Step 8: Save configuration for reproducibility
		checkpoint_manager.save_training_config("distillation", run_name, distillation_config)

		# Step 9: Initialize and run distillation training
		logger.info("Initializing distillation trainer...")
		trainer = ParentChildTrainer(distillation_config)

		logger.info("Starting distillation training loop...")
		trainer.train()  # Note: This is the synchronous version that handles async internally

		logger.info("Distillation training completed successfully!")

		# Step 10: Save final summary
		final_checkpoint = checkpoint_manager.find_latest_checkpoint("distillation", run_name)
		logger.info(f"Final checkpoint saved: {final_checkpoint}")

	except Exception as e:
		logger.error(f"Distillation training failed: {str(e)}")
		raise

# ============================================================================
# FUNDAMENTAL COMPONENT 4: MULTI-FORMAT TRAINING FUNCTION
# ============================================================================

def run_multiformat_training(
	distilled_checkpoint: Optional[str] = None,
	resume_from: Optional[str] = None,
	run_name: str = "multiformat_run",
	max_steps: int = 30000,
	batch_size: int = 6,
	learning_rate: float = 2e-5,
	use_wandb: bool = True
) -> None:
	"""
	Execute comprehensive multi-format training for specialized capabilities.

	Multi-format training represents the specialization phase, where the model
	learns to handle diverse interaction patterns: instructions, conversations,
	and agentic reasoning tasks.

	Educational Framework:
	----------------------
	1. **Task Diversity**: Multiple formats teach different interaction patterns
	2. **Capability Specialization**: Each format develops specific skills
	3. **Integration**: Unified model handles all task types seamlessly
	4. **Advanced Reasoning**: Builds on distilled knowledge for complex tasks

	Args:
		distilled_checkpoint: Path to distilled model checkpoint
		resume_from: Path to checkpoint for resuming training
		run_name: Identifier for this training run
		max_steps: Maximum training steps
		batch_size: Training batch size
		learning_rate: Learning rate for optimization
		use_wandb: Whether to use Weights & Biases logging
	"""

	logger = logging.getLogger(f"{__name__}.multiformat")
	logger.info("=" * 80)
	logger.info("STARTING MULTI-FORMAT TRAINING MODE")
	logger.info("=" * 80)

	# Step 1: Initialize checkpoint management
	checkpoint_manager = CheckpointManager("./checkpoints")
	checkpoint_dir = checkpoint_manager.get_checkpoint_dir("multiformat", run_name)

	logger.info(f"Multi-format run: {run_name}")
	logger.info(f"Checkpoint directory: {checkpoint_dir}")

	try:
		# Step 2: Configure advanced model for multi-format tasks
		model_config = DiffusionConfig(
			# Model architecture (loads from distilled model)
			vocab_size=50257,
			hidden_dim=768,
			num_layers=12,
			num_heads=12,
			max_length=1024,

			# Full diffusion capability for complex reasoning
			diffusion_steps=14,  # Maximum complexity
			use_kv_cache=True,
			confidence_calibration=True,

			# Advanced memory for complex task handling
			st_memory_size=200,
			mt_memory_size=1000,
			lt_memory_size=4000,

			# Fine-tuned stability
			dropout=0.1,
			max_grad_norm=1.0
		)

		# Step 3: Configure comprehensive training
		training_config = TrainingConfig(
			# Model configuration
			model_config=model_config,

			# Fine-tuning hyperparameters
			learning_rate=learning_rate,  # Lower LR for fine-tuning
			weight_decay=0.01,
			warmup_steps=500,  # Shorter warmup for fine-tuning
			max_steps=max_steps,
			gradient_accumulation_steps=4,
			max_grad_norm=1.0,

			# Full diffusion curriculum
			diffusion_curriculum=True,
			start_diffusion_steps=8,  # Start from mid-complexity
			end_diffusion_steps=14,   # Build to full complexity
			curriculum_schedule="cosine",

			# Data configuration
			batch_size=batch_size,
			max_length=1024,
			tokenizer_name="gpt2",

			# Balanced format weights for comprehensive capabilities
			pretraining_weight=0.2,   # Maintain language foundation
			instruction_weight=0.3,   # Strong instruction following
			conversation_weight=0.3,  # Advanced conversation skills
			agentic_weight=0.2,       # Complex reasoning and tool use

			# Loss weights for advanced training
			generation_loss_weight=1.0,
			consistency_loss_weight=0.15,  # Higher consistency for complex tasks
			memory_loss_weight=0.1,        # Enhanced memory utilization
			confidence_loss_weight=0.1,    # Better calibration

			# Evaluation and checkpointing
			eval_steps=1000,
			save_steps=2500,
			logging_steps=100,
			save_total_limit=5,

			# Paths
			output_dir=str(checkpoint_dir),
			data_dir="./data",
			cache_dir="./cache",
			resume_from_checkpoint=resume_from,

			# Logging
			wandb_project="diffusion-llm-multiformat" if use_wandb else None,
			run_name=run_name,

			# Performance optimization
			bf16=True,
			use_gradient_checkpointing=True,  # Enable for memory efficiency
			dataloader_num_workers=4,

			# Advanced training features
			use_data_augmentation=True,  # Enable for robustness
			corruption_probability=0.2,  # Light corruption for variety
			max_corruption_ratio=0.15,
			use_dynamic_batching=True,
			min_batch_size=4,
			max_batch_size=12,

			# Comprehensive evaluation
			compute_generation_metrics=True,
			compute_calibration_metrics=True,
			max_generation_samples=100
		)

		# Step 4: Load distilled checkpoint if provided
		if distilled_checkpoint:
			logger.info(f"Loading distilled model from: {distilled_checkpoint}")
			training_config.resume_from_checkpoint = distilled_checkpoint

		# Step 5: Save configuration for reproducibility
		checkpoint_manager.save_training_config("multiformat", run_name, training_config)

		# Step 6: Initialize and run multi-format training
		logger.info("Initializing multi-format trainer...")
		trainer = DiffusionTrainer(training_config)

		logger.info("Starting multi-format training loop...")
		trainer.train()

		logger.info("Multi-format training completed successfully!")

		# Step 7: Save final summary
		final_checkpoint = checkpoint_manager.find_latest_checkpoint("multiformat", run_name)
		logger.info(f"Final checkpoint saved: {final_checkpoint}")

	except Exception as e:
		logger.error(f"Multi-format training failed: {str(e)}")
		raise

# ============================================================================
# FUNDAMENTAL COMPONENT 5: UNIFIED TRAINING PIPELINE
# ============================================================================

async def run_complete_training_pipeline(
	pipeline_name: str = "complete_pipeline",
	dataset_name: str = "wikitext",
	dataset_variant: str = "wikitext-103-raw-v1",
	parent_model: str = "DeepSeek-R1-0528-Qwen3-8B-BF16:latest",
	pretraining_steps: int = 20000,
	distillation_steps: int = 15000,
	multiformat_steps: int = 10000,
	use_wandb: bool = True
) -> Dict[str, str]:
	"""
	Execute the complete training pipeline from pretraining to specialization.

	This function demonstrates the systematic progression through all training phases,
	showing how each mode builds upon the previous to create increasingly capable models.

	Educational Pipeline:
	--------------------
	1. **Pretraining**: Establishes fundamental language understanding
	2. **Distillation**: Transfers advanced reasoning capabilities
	3. **Multi-Format**: Specializes for diverse interaction patterns

	Returns:
		Dictionary mapping training phases to final checkpoint paths
	"""

	logger = logging.getLogger(f"{__name__}.pipeline")
	logger.info("=" * 80)
	logger.info("STARTING COMPLETE TRAINING PIPELINE")
	logger.info("=" * 80)

	checkpoint_paths = {}
	checkpoint_manager = CheckpointManager("./checkpoints")

	try:
		# Phase 1: Pretraining
		logger.info("Phase 1: Pretraining Foundation")
		logger.info("-" * 40)

		pretraining_run = f"{pipeline_name}_pretraining"
		run_pretraining(
			dataset_name=dataset_name,
			dataset_variant=dataset_variant,
			run_name=pretraining_run,
			max_steps=pretraining_steps,
			use_wandb=use_wandb
		)

		pretrained_checkpoint = checkpoint_manager.find_latest_checkpoint("pretraining", pretraining_run)
		checkpoint_paths["pretraining"] = str(pretrained_checkpoint)
		logger.info(f"Pretraining completed: {pretrained_checkpoint}")

		# Phase 2: Distillation Training
		logger.info("Phase 2: Knowledge Distillation")
		logger.info("-" * 40)

		distillation_run = f"{pipeline_name}_distillation"
		await run_distillation_training(
			parent_model=parent_model,
			pretrained_checkpoint=str(pretrained_checkpoint),
			run_name=distillation_run,
			max_steps=distillation_steps,
			use_wandb=use_wandb
		)

		distilled_checkpoint = checkpoint_manager.find_latest_checkpoint("distillation", distillation_run)
		checkpoint_paths["distillation"] = str(distilled_checkpoint)
		logger.info(f"Distillation completed: {distilled_checkpoint}")

		# Phase 3: Multi-Format Training
		logger.info("Phase 3: Multi-Format Specialization")
		logger.info("-" * 40)

		multiformat_run = f"{pipeline_name}_multiformat"
		run_multiformat_training(
			distilled_checkpoint=str(distilled_checkpoint),
			run_name=multiformat_run,
			max_steps=multiformat_steps,
			use_wandb=use_wandb
		)

		final_checkpoint = checkpoint_manager.find_latest_checkpoint("multiformat", multiformat_run)
		checkpoint_paths["multiformat"] = str(final_checkpoint)
		logger.info(f"Multi-format completed: {final_checkpoint}")

		# Pipeline Summary
		logger.info("=" * 80)
		logger.info("COMPLETE PIPELINE FINISHED SUCCESSFULLY")
		logger.info("=" * 80)

		for phase, checkpoint in checkpoint_paths.items():
			logger.info(f"{phase.capitalize()}: {checkpoint}")

		return checkpoint_paths

	except Exception as e:
		logger.error(f"Pipeline failed at phase: {str(e)}")
		raise

# ============================================================================
# FUNDAMENTAL COMPONENT 6: COMMAND LINE INTERFACE
# ============================================================================

def create_argument_parser() -> argparse.ArgumentParser:
	"""Create comprehensive command line interface for all training modes."""

	parser = argparse.ArgumentParser(
		description="Unified Diffusion LLM Training System",
		formatter_class=argparse.RawDescriptionHelpFormatter,
		epilog="""
Training Mode Descriptions:
--------------------------
pretraining:  Large-scale unsupervised learning on clean datasets
distillation: Knowledge transfer from advanced teacher models
multiformat:  Multi-task training on diverse interaction patterns
pipeline:     Complete training progression through all phases

Example Usage:
--------------
# Run pretraining on WikiText-103
python main.py pretraining --dataset wikitext --variant wikitext-103-raw-v1

# Run distillation training using DeepSeek-R1
python main.py distillation --parent-model DeepSeek-R1-0528-Qwen3-8B-BF16:latest

# Run multi-format training from distilled checkpoint
python main.py multiformat --checkpoint ./checkpoints/distillation/run/checkpoint-15000.pt

# Run complete pipeline
python main.py pipeline --pipeline-name my_model_v1
		"""
	)

	# Global arguments
	parser.add_argument(
		"mode",
		choices=["pretraining", "distillation", "multiformat", "pipeline"],
		help="Training mode to execute"
	)

	parser.add_argument(
		"--run-name",
		type=str,
		default=None,
		help="Name for this training run (auto-generated if not specified)"
	)

	parser.add_argument(
		"--resume-from",
		type=str,
		default=None,
		help="Path to checkpoint for resuming training"
	)

	parser.add_argument(
		"--log-level",
		choices=["DEBUG", "INFO", "WARNING", "ERROR"],
		default="INFO",
		help="Logging level"
	)

	parser.add_argument(
		"--log-file",
		type=str,
		default=None,
		help="Path to log file (logs to console if not specified)"
	)

	parser.add_argument(
		"--no-wandb",
		action="store_true",
		help="Disable Weights & Biases logging"
	)

	# Training parameters
	parser.add_argument(
		"--max-steps",
		type=int,
		default=None,
		help="Maximum training steps"
	)

	parser.add_argument(
		"--batch-size",
		type=int,
		default=None,
		help="Training batch size"
	)

	parser.add_argument(
		"--learning-rate",
		type=float,
		default=None,
		help="Learning rate"
	)

	# Pretraining specific
	parser.add_argument(
		"--dataset",
		type=str,
		default="wikitext",
		help="Dataset name for pretraining"
	)

	parser.add_argument(
		"--variant",
		type=str,
		default="wikitext-103-raw-v1",
		help="Dataset variant for pretraining"
	)

	# Distillation specific
	parser.add_argument(
		"--parent-model",
		type=str,
		default="DeepSeek-R1-0528-Qwen3-8B-BF16:latest",
		help="Parent model for distillation"
	)

	parser.add_argument(
		"--ollama-host",
		type=str,
		default="http://localhost:11434",
		help="Ollama server host"
	)

	parser.add_argument(
		"--pretrained-checkpoint",
		type=str,
		default=None,
		help="Pretrained checkpoint for distillation"
	)

	# Multi-format specific
	parser.add_argument(
		"--distilled-checkpoint",
		type=str,
		default=None,
		help="Distilled checkpoint for multi-format training"
	)

	# Pipeline specific
	parser.add_argument(
		"--pipeline-name",
		type=str,
		default="pipeline",
		help="Name for complete pipeline run"
	)

	parser.add_argument(
		"--pretraining-steps",
		type=int,
		default=20000,
		help="Steps for pretraining phase"
	)

	parser.add_argument(
		"--distillation-steps",
		type=int,
		default=15000,
		help="Steps for distillation phase"
	)

	parser.add_argument(
		"--multiformat-steps",
		type=int,
		default=10000,
		help="Steps for multi-format phase"
	)

	return parser

async def main():
	"""Main entry point with comprehensive argument handling."""

	# Parse command line arguments
	parser = create_argument_parser()
	args = parser.parse_args()

	# Setup logging
	logger = setup_logging(args.log_level, args.log_file)

	# Display training mode descriptions
	mode_descriptions = TrainingMode.get_descriptions()
	logger.info(f"Selected mode: {args.mode}")
	logger.info(f"Description: {mode_descriptions[args.mode]}")

	try:
		# Generate run name if not provided
		if not args.run_name:
			timestamp = int(time.time())
			args.run_name = f"{args.mode}_{timestamp}"

		# Execute selected training mode
		if args.mode == "pretraining":
			run_pretraining(
				dataset_name=args.dataset,
				dataset_variant=args.variant,
				resume_from=args.resume_from,
				run_name=args.run_name,
				max_steps=args.max_steps or 50000,
				batch_size=args.batch_size or 8,
				learning_rate=args.learning_rate or 5e-5,
				use_wandb=not args.no_wandb
			)

		elif args.mode == "distillation":
			await run_distillation_training(
				parent_model=args.parent_model,
				ollama_host=args.ollama_host,
				pretrained_checkpoint=args.pretrained_checkpoint,
				resume_from=args.resume_from,
				run_name=args.run_name,
				max_steps=args.max_steps or 25000,
				batch_size=args.batch_size or 4,
				learning_rate=args.learning_rate or 3e-5,
				use_wandb=not args.no_wandb
			)

		elif args.mode == "multiformat":
			run_multiformat_training(
				distilled_checkpoint=args.distilled_checkpoint,
				resume_from=args.resume_from,
				run_name=args.run_name,
				max_steps=args.max_steps or 30000,
				batch_size=args.batch_size or 6,
				learning_rate=args.learning_rate or 2e-5,
				use_wandb=not args.no_wandb
			)

		elif args.mode == "pipeline":
			checkpoint_paths = await run_complete_training_pipeline(
				pipeline_name=args.pipeline_name,
				dataset_name=args.dataset,
				dataset_variant=args.variant,
				parent_model=args.parent_model,
				pretraining_steps=args.pretraining_steps,
				distillation_steps=args.distillation_steps,
				multiformat_steps=args.multiformat_steps,
				use_wandb=not args.no_wandb
			)

			logger.info("Pipeline Summary:")
			for phase, path in checkpoint_paths.items():
				logger.info(f"  {phase}: {path}")

	except KeyboardInterrupt:
		logger.info("Training interrupted by user")
	except Exception as e:
		logger.error(f"Training failed: {str(e)}")
		sys.exit(1)

if __name__ == "__main__":
	# Handle async main properly
	asyncio.run(main())
